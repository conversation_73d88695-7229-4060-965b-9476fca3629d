#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/vue-metamorph@3.2.0_eslint@9.27.0_jiti@2.4.2_/node_modules/vue-metamorph/scripts/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/vue-metamorph@3.2.0_eslint@9.27.0_jiti@2.4.2_/node_modules/vue-metamorph/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/vue-metamorph@3.2.0_eslint@9.27.0_jiti@2.4.2_/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/vue-metamorph@3.2.0_eslint@9.27.0_jiti@2.4.2_/node_modules/vue-metamorph/scripts/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/vue-metamorph@3.2.0_eslint@9.27.0_jiti@2.4.2_/node_modules/vue-metamorph/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/vue-metamorph@3.2.0_eslint@9.27.0_jiti@2.4.2_/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../scripts/scaffold.js" "$@"
else
  exec node  "$basedir/../../scripts/scaffold.js" "$@"
fi
