package config

import (
	"os"
	"path/filepath"

	"github.com/spf13/viper"
)

// Config holds all configuration for the portal
type Config struct {
	// Server configuration
	Host   string `mapstructure:"host"`
	Port   int    `mapstructure:"port"`
	DataDir string `mapstructure:"data_dir"`
	
	// ETL configuration
	ETLBinaries map[string]string `mapstructure:"etl_binaries"`
	WorkDir     string            `mapstructure:"work_dir"`
}

// DefaultConfig returns a configuration with default values
func DefaultConfig() *Config {
	homeDir, _ := os.UserHomeDir()
	
	return &Config{
		Host:   "localhost",
		Port:   8080,
		DataDir: filepath.Join(homeDir, ".etl-portal"),
		ETLBinaries: map[string]string{},
		WorkDir:     "workdir",
	}
}

// Load loads configuration from file and environment variables
func Load() (*Config, error) {
	config := DefaultConfig()
	
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./config")
	
	// Environment variables
	viper.SetEnvPrefix("ETL_PORTAL")
	viper.AutomaticEnv()
	
	// Read config file if it exists
	if err := viper.ReadInConfig(); err != nil {
		// It's okay if config file doesn't exist
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, err
		}
	}
	
	// Unmarshal config
	if err := viper.Unmarshal(config); err != nil {
		return nil, err
	}
	
	return config, nil
}

// Save saves the current configuration to a file
func (c *Config) Save() error {
	for k, v := range map[string]interface{}{
		"host":         c.Host,
		"port":         c.Port,
		"data_dir":     c.DataDir,
		"etl_binaries": c.ETLBinaries,
		"work_dir":     c.WorkDir,
	} {
		viper.Set(k, v)
	}
	
	return viper.WriteConfigAs("config.yaml")
}
