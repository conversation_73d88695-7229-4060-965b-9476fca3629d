{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "brand", "rawType": "object", "type": "string"}, {"name": "circle", "rawType": "object", "type": "unknown"}, {"name": "region", "rawType": "object", "type": "unknown"}, {"name": "lmtd", "rawType": "float64", "type": "float"}, {"name": "mtd", "rawType": "float64", "type": "float"}, {"name": "dt_id", "rawType": "int64", "type": "integer"}, {"name": "parameter", "rawType": "object", "type": "string"}], "conversionMethod": "pd.DataFrame", "ref": "86fb4b0d-51e4-468a-a3d7-8241e603741d", "rows": [["0", "3ID", "KALISUMAPA", "MAPA", "63.0", "74.0", "20250316", "Site 3-QSSO"], ["1", "3ID", "JAKARTA RAYA", "INNER JAKARTA", "4875371799.999999", "13514982209.009012", "20250316", "Secondary"], ["2", "3ID", "JAKARTA RAYA", "WEST JAVA", "42993.0", "169278.0", "20250316", "RGU GA"], ["3", "IM3", "JAVA", "EAST JAVA", "143995.0", "232552.0", "20250316", "RGU GA"], ["4", "3ID", "JAKARTA RAYA", "INNER JAKARTA", "280.0", "323.0", "20250316", "Site 5-QURO"], ["5", "IM3", "KALISUMAPA", "KALIMANTAN", "1126.0", "2113.0", "20250316", "Site 3-QSSO"], ["6", "IM3", "SUMATERA", "CENTRAL SUMATERA", "311.0", "567.0", "20250316", "Site 3-QSSO"], ["7", "IM3", "JAKARTA RAYA", "WEST JAVA", "434.0", "606.0", "20250316", "Site 5-QURO"], ["8", "3ID", "KALISUMAPA", "KALIMANTAN", "10222095554.054058", "11478151882.88288", "20250316", "Secondary"], ["9", "IM3", "KALISUMAPA", "MAPA", "189.0", "484.0", "20250316", "Site 3-QSSO"], ["10", "IM3", "JAKARTA RAYA", "INNER JAKARTA", "596.0", "784.0", "20250316", "Site 3-QSSO"], ["11", "3ID", "JAVA", "BALI NUSRA", "5094612505.405405", "7401568783.783784", "20250316", "Secondary"], ["12", "IM3", "JAVA", "CENTRAL JAVA", "169417.0", "290409.0", "20250316", "RGU GA"], ["13", "IM3", "SUMATERA", "SOUTH SUMATERA", "60353.0", "130274.0", "20250316", "RGU GA"], ["14", "3ID", "JAVA", "CENTRAL JAVA", "105858.0", "117959.0", "20250316", "RGU GA"], ["15", "3ID", "JAVA", "BALI NUSRA", "208.0", "194.0", "20250316", "Site 5-QURO"], ["16", "IM3", null, null, "0.0", "0.0", "20250316", "Site 3-QSSO"], ["17", "3ID", "JAKARTA RAYA", "WEST JAVA", "15437574296.3964", "23164852639.63964", "20250316", "Secondary"], ["18", "IM3", "JAKARTA RAYA", "OUTER JAKARTA", "1402.0", "1444.0", "20250316", "Site 5-QURO"], ["19", "3ID", "JAKARTA RAYA", "WEST JAVA", "906.0", "536.0", "20250316", "Site 3-QSSO"], ["20", "IM3", null, null, "0.0", "0.0", "20250316", "Site 5-QURO"], ["21", "3ID", "JAKARTA RAYA", "WEST JAVA", "721.0", "843.0", "20250316", "Site 5-QURO"], ["22", "IM3", "JAVA", "EAST JAVA", "1694.0", "1674.0", "20250316", "Site 5-QURO"], ["23", "IM3", "KALISUMAPA", "KALIMANTAN", "1574.0", "1695.0", "20250316", "Site 5-QURO"], ["24", "3ID", "JAKARTA RAYA", "OUTER JAKARTA", "28749058558.558563", "39495693145.04505", "20250316", "Secondary"], ["25", "IM3", "JAVA", "EAST JAVA", "58468636886.48624", "64290111211.711685", "20250316", "Secondary"], ["26", "3ID", null, null, "7387747.747747748", "11083009.00900901", "20250316", "Secondary"], ["27", "3ID", "SUMATERA", "SOUTH SUMATERA", "744.0", "829.0", "20250316", "Site 5-QURO"], ["28", "3ID", "JAVA", "CENTRAL JAVA", "36930459570.27027", "35435575023.42341", "20250316", "Secondary"], ["29", "IM3", "SUMATERA", "SOUTH SUMATERA", "1203.0", "1407.0", "20250316", "Site 5-QURO"], ["30", "3ID", "KALISUMAPA", "MAPA", "114.0", "113.0", "20250316", "Site 5-QURO"], ["31", "IM3", "SUMATERA", "CENTRAL SUMATERA", "32314.0", "61637.0", "20250316", "RGU GA"], ["32", "3ID", "JAVA", "BALI NUSRA", "42770.0", "55532.0", "20250316", "RGU GA"], ["33", "IM3", null, null, "1498.0", "11674.0", "20250316", "RGU GA"], ["34", "IM3", "JAVA", "EAST JAVA", "1782.0", "2289.0", "20250316", "Site 3-QSSO"], ["35", "IM3", "JAVA", "BALI NUSRA", "14913797490.99077", "17491621755.855686", "20250316", "Secondary"], ["36", "3ID", "JAVA", "BALI NUSRA", "299.0", "685.0", "20250316", "Site 3-QSSO"], ["37", "IM3", "KALISUMAPA", "SULAWESI", "861.0", "864.0", "20250316", "Site 5-QURO"], ["38", "3ID", "JAKARTA RAYA", "OUTER JAKARTA", "1583.0", "1504.0", "20250316", "Site 3-QSSO"], ["39", "IM3", "JAKARTA RAYA", "INNER JAKARTA", "378.0", "395.0", "20250316", "Site 5-QURO"], ["40", "IM3", "KALISUMAPA", "MAPA", "12767.0", "27180.0", "20250316", "RGU GA"], ["41", "3ID", "SUMATERA", "NORTH SUMATERA", "87129.0", "106553.0", "20250316", "RGU GA"], ["42", "IM3", "JAVA", "CENTRAL JAVA", "3218.0", "3988.0", "20250316", "Site 3-QSSO"], ["43", "IM3", "KALISUMAPA", "SULAWESI", "18994622994.594536", "33224658456.756645", "20250316", "Secondary"], ["44", "3ID", "JAVA", "CENTRAL JAVA", "1658.0", "1407.0", "20250316", "Site 3-QSSO"], ["45", "3ID", "SUMATERA", "SOUTH SUMATERA", "44363.0", "50520.0", "20250316", "RGU GA"], ["46", "IM3", "JAKARTA RAYA", "OUTER JAKARTA", "1771.0", "2839.0", "20250316", "Site 3-QSSO"], ["47", "3ID", "JAVA", "EAST JAVA", "630.0", "383.0", "20250316", "Site 3-QSSO"], ["48", "3ID", null, null, "0.0", "0.0", "20250316", "Site 3-QSSO"], ["49", "3ID", "KALISUMAPA", "KALIMANTAN", "582.0", "561.0", "20250316", "Site 5-QURO"]], "shape": {"columns": 7, "rows": 294}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>brand</th>\n", "      <th>circle</th>\n", "      <th>region</th>\n", "      <th>lmtd</th>\n", "      <th>mtd</th>\n", "      <th>dt_id</th>\n", "      <th>parameter</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3ID</td>\n", "      <td>KALISUMAPA</td>\n", "      <td>MAPA</td>\n", "      <td>6.300000e+01</td>\n", "      <td>7.400000e+01</td>\n", "      <td>20250316</td>\n", "      <td>Site 3-QSSO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3ID</td>\n", "      <td>JAKARTA RAYA</td>\n", "      <td>INNER JAKARTA</td>\n", "      <td>4.875372e+09</td>\n", "      <td>1.351498e+10</td>\n", "      <td>20250316</td>\n", "      <td>Secondary</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3ID</td>\n", "      <td>JAKARTA RAYA</td>\n", "      <td>WEST JAVA</td>\n", "      <td>4.299300e+04</td>\n", "      <td>1.692780e+05</td>\n", "      <td>20250316</td>\n", "      <td>RGU GA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>IM3</td>\n", "      <td>JAVA</td>\n", "      <td>EAST JAVA</td>\n", "      <td>1.439950e+05</td>\n", "      <td>2.325520e+05</td>\n", "      <td>20250316</td>\n", "      <td>RGU GA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3ID</td>\n", "      <td>JAKARTA RAYA</td>\n", "      <td>INNER JAKARTA</td>\n", "      <td>2.800000e+02</td>\n", "      <td>3.230000e+02</td>\n", "      <td>20250316</td>\n", "      <td>Site 5-QURO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>289</th>\n", "      <td>3ID</td>\n", "      <td>JAVA</td>\n", "      <td>EAST JAVA</td>\n", "      <td>4.380000e+02</td>\n", "      <td>4.380000e+02</td>\n", "      <td>20250316</td>\n", "      <td>DSE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>290</th>\n", "      <td>3ID</td>\n", "      <td>SUMATERA</td>\n", "      <td>SOUTH SUMATERA</td>\n", "      <td>2.890000e+02</td>\n", "      <td>3.380000e+02</td>\n", "      <td>20250316</td>\n", "      <td>DSE w/ &lt;12 GAD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>291</th>\n", "      <td>3ID</td>\n", "      <td>SUMATERA</td>\n", "      <td>CENTRAL SUMATERA</td>\n", "      <td>3.020000e+02</td>\n", "      <td>3.020000e+02</td>\n", "      <td>20250316</td>\n", "      <td>DSE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>292</th>\n", "      <td>3ID</td>\n", "      <td>SUMATERA</td>\n", "      <td>NORTH SUMATERA</td>\n", "      <td>3.310000e+02</td>\n", "      <td>3.310000e+02</td>\n", "      <td>20250316</td>\n", "      <td>DSE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>293</th>\n", "      <td>3ID</td>\n", "      <td>SUMATERA</td>\n", "      <td>SOUTH SUMATERA</td>\n", "      <td>3.640000e+02</td>\n", "      <td>3.640000e+02</td>\n", "      <td>20250316</td>\n", "      <td>DSE</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>294 rows × 7 columns</p>\n", "</div>"], "text/plain": ["    brand        circle            region          lmtd           mtd  \\\n", "0     3ID    KALISUMAPA              MAPA  6.300000e+01  7.400000e+01   \n", "1     3ID  JAKARTA RAYA     INNER JAKARTA  4.875372e+09  1.351498e+10   \n", "2     3ID  JAKARTA RAYA         WEST JAVA  4.299300e+04  1.692780e+05   \n", "3     IM3          JAVA         EAST JAVA  1.439950e+05  2.325520e+05   \n", "4     3ID  JAKARTA RAYA     INNER JAKARTA  2.800000e+02  3.230000e+02   \n", "..    ...           ...               ...           ...           ...   \n", "289   3ID          JAVA         EAST JAVA  4.380000e+02  4.380000e+02   \n", "290   3ID      SUMATERA    SOUTH SUMATERA  2.890000e+02  3.380000e+02   \n", "291   3ID      SUMATERA  CENTRAL SUMATERA  3.020000e+02  3.020000e+02   \n", "292   3ID      SUMATERA    NORTH SUMATERA  3.310000e+02  3.310000e+02   \n", "293   3ID      SUMATERA    SOUTH SUMATERA  3.640000e+02  3.640000e+02   \n", "\n", "        dt_id       parameter  \n", "0    20250316     Site 3-QSSO  \n", "1    20250316       Secondary  \n", "2    20250316          RGU GA  \n", "3    20250316          RGU GA  \n", "4    20250316     Site 5-QURO  \n", "..        ...             ...  \n", "289  20250316             DSE  \n", "290  20250316  DSE w/ <12 GAD  \n", "291  20250316             DSE  \n", "292  20250316             DSE  \n", "293  20250316             DSE  \n", "\n", "[294 rows x 7 columns]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "df = pd.read_csv('distrib_20250318160323.csv')\n", "df"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "brand", "rawType": "object", "type": "string"}, {"name": "('region', 'Addressable Site')", "rawType": "int64", "type": "integer"}, {"name": "('region', 'DSE')", "rawType": "int64", "type": "integer"}, {"name": "('region', 'DSE w/ <12 GAD')", "rawType": "int64", "type": "integer"}, {"name": "('region', 'DSE w/ <5 Mn Secondary')", "rawType": "int64", "type": "integer"}, {"name": "('region', 'RGU GA')", "rawType": "int64", "type": "integer"}, {"name": "('region', 'SDP <350 GA')", "rawType": "int64", "type": "integer"}, {"name": "('region', 'SDP <75 Mn')", "rawType": "int64", "type": "integer"}, {"name": "('region', 'SDP/3KIOSK')", "rawType": "int64", "type": "integer"}, {"name": "('region', 'Secondary')", "rawType": "int64", "type": "integer"}, {"name": "('region', 'Site 3-QSSO')", "rawType": "int64", "type": "integer"}, {"name": "('region', 'Site 5-QURO')", "rawType": "int64", "type": "integer"}, {"name": "('region', 'Site w/ <1 GAD/Day')", "rawType": "int64", "type": "integer"}], "conversionMethod": "pd.DataFrame", "ref": "301d7e65-5efb-4e97-ae55-9b9af634ecdb", "rows": [["3ID", "12", "12", "12", "12", "12", "11", "11", "11", "12", "12", "12", "12"], ["IM3", "12", "12", "12", "12", "12", "12", "12", "12", "12", "12", "12", "12"]], "shape": {"columns": 12, "rows": 2}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"12\" halign=\"left\">region</th>\n", "    </tr>\n", "    <tr>\n", "      <th>parameter</th>\n", "      <th>Addressable Site</th>\n", "      <th>DSE</th>\n", "      <th>DSE w/ &lt;12 GAD</th>\n", "      <th>DSE w/ &lt;5 Mn Secondary</th>\n", "      <th>RGU GA</th>\n", "      <th>SDP &lt;350 GA</th>\n", "      <th>SDP &lt;75 Mn</th>\n", "      <th>SDP/3KIOSK</th>\n", "      <th>Secondary</th>\n", "      <th>Site 3-QSSO</th>\n", "      <th>Site 5-QURO</th>\n", "      <th>Site w/ &lt;1 GAD/Day</th>\n", "    </tr>\n", "    <tr>\n", "      <th>brand</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3ID</th>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>11</td>\n", "      <td>11</td>\n", "      <td>11</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IM3</th>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    region                                                   \\\n", "parameter Addressable Site DSE DSE w/ <12 GAD DSE w/ <5 Mn Secondary RGU GA   \n", "brand                                                                         \n", "3ID                     12  12             12                     12     12   \n", "IM3                     12  12             12                     12     12   \n", "\n", "                                                                               \\\n", "parameter SDP <350 GA SDP <75 Mn SDP/3KIOSK Secondary Site 3-QSSO Site 5-QURO   \n", "brand                                                                           \n", "3ID                11         11         11        12          12          12   \n", "IM3                12         12         12        12          12          12   \n", "\n", "                              \n", "parameter Site w/ <1 GAD/Day  \n", "brand                         \n", "3ID                       12  \n", "IM3                       12  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["pv = df.pivot_table(index=['brand'], columns=['parameter'], values=['region'], aggfunc='count')\n", "pv"]}], "metadata": {"kernelspec": {"display_name": "ds", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}