hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@antfu/utils@0.7.10':
    '@antfu/utils': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.27.2':
    '@babel/compat-data': private
  '@babel/core@7.27.1':
    '@babel/core': private
  '@babel/generator@7.27.1':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.27.1':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.27.1)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.1(@babel/core@7.27.1)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.27.1)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.1':
    '@babel/helpers': private
  '@babel/parser@7.27.2':
    '@babel/parser': private
  '@babel/plugin-proposal-decorators@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-proposal-decorators': private
  '@babel/plugin-syntax-decorators@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-syntax-decorators': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.27.1)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-transform-modules-commonjs@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-transform-modules-commonjs': private
  '@babel/plugin-transform-typescript@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-transform-typescript': private
  '@babel/preset-typescript@7.27.1(@babel/core@7.27.1)':
    '@babel/preset-typescript': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.1':
    '@babel/traverse': private
  '@babel/types@7.27.1':
    '@babel/types': private
  '@esbuild/aix-ppc64@0.25.4':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.4':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.4':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.4':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.4':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.4':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.4':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.4':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.4':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.4':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.4':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.4':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.4':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.4':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.4':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.4':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.4':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.4':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.4':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.4':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.4':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.4':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.4':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.4':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.4':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.27.0(jiti@2.4.2))':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/config-array@0.20.0':
    '@eslint/config-array': public
  '@eslint/config-helpers@0.2.2':
    '@eslint/config-helpers': public
  '@eslint/core@0.14.0':
    '@eslint/core': public
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': public
  '@eslint/js@9.27.0':
    '@eslint/js': public
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': public
  '@eslint/plugin-kit@0.3.1':
    '@eslint/plugin-kit': public
  '@floating-ui/core@1.7.0':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.0':
    '@floating-ui/dom': private
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': private
  '@floating-ui/vue@1.1.6(vue@3.5.14(typescript@5.8.3))':
    '@floating-ui/vue': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@internationalized/date@3.8.1':
    '@internationalized/date': private
  '@internationalized/number@3.6.2':
    '@internationalized/number': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@polka/url@1.0.0-next.29':
    '@polka/url': private
  '@rollup/pluginutils@5.1.4(rollup@4.41.0)':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.41.0':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.41.0':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.41.0':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.41.0':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.41.0':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.41.0':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.41.0':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.41.0':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.41.0':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.41.0':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.41.0':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.41.0':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.41.0':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.41.0':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.41.0':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.41.0':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.41.0':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.41.0':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.41.0':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.41.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@sec-ant/readable-stream@0.4.1':
    '@sec-ant/readable-stream': private
  '@sindresorhus/merge-streams@4.0.0':
    '@sindresorhus/merge-streams': private
  '@swc/helpers@0.5.17':
    '@swc/helpers': private
  '@tailwindcss/node@4.1.8':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-android-arm64@4.1.8':
    '@tailwindcss/oxide-android-arm64': private
  '@tailwindcss/oxide-darwin-arm64@4.1.8':
    '@tailwindcss/oxide-darwin-arm64': private
  '@tailwindcss/oxide-darwin-x64@4.1.8':
    '@tailwindcss/oxide-darwin-x64': private
  '@tailwindcss/oxide-freebsd-x64@4.1.8':
    '@tailwindcss/oxide-freebsd-x64': private
  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.8':
    '@tailwindcss/oxide-linux-arm-gnueabihf': private
  '@tailwindcss/oxide-linux-arm64-gnu@4.1.8':
    '@tailwindcss/oxide-linux-arm64-gnu': private
  '@tailwindcss/oxide-linux-arm64-musl@4.1.8':
    '@tailwindcss/oxide-linux-arm64-musl': private
  '@tailwindcss/oxide-linux-x64-gnu@4.1.8':
    '@tailwindcss/oxide-linux-x64-gnu': private
  '@tailwindcss/oxide-linux-x64-musl@4.1.8':
    '@tailwindcss/oxide-linux-x64-musl': private
  '@tailwindcss/oxide-wasm32-wasi@4.1.8':
    '@tailwindcss/oxide-wasm32-wasi': private
  '@tailwindcss/oxide-win32-arm64-msvc@4.1.8':
    '@tailwindcss/oxide-win32-arm64-msvc': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.8':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.8':
    '@tailwindcss/oxide': private
  '@tanstack/virtual-core@3.13.9':
    '@tanstack/virtual-core': private
  '@tanstack/vue-virtual@3.13.9(vue@3.5.14(typescript@5.8.3))':
    '@tanstack/vue-virtual': private
  '@ts-morph/common@0.25.0':
    '@ts-morph/common': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/web-bluetooth@0.0.21':
    '@types/web-bluetooth': private
  '@unovue/detypes@0.8.5':
    '@unovue/detypes': private
  '@vitest/expect@3.1.4':
    '@vitest/expect': private
  '@vitest/mocker@3.1.4(vite@6.3.5(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(stylus@0.57.0)(yaml@2.8.0))':
    '@vitest/mocker': private
  '@vitest/pretty-format@3.1.4':
    '@vitest/pretty-format': private
  '@vitest/runner@3.1.4':
    '@vitest/runner': private
  '@vitest/snapshot@3.1.4':
    '@vitest/snapshot': private
  '@vitest/spy@3.1.4':
    '@vitest/spy': private
  '@vitest/ui@3.1.4(vitest@3.1.4)':
    '@vitest/ui': private
  '@vitest/utils@3.1.4':
    '@vitest/utils': private
  '@volar/language-core@2.4.14':
    '@volar/language-core': private
  '@volar/source-map@2.4.14':
    '@volar/source-map': private
  '@volar/typescript@2.4.14':
    '@volar/typescript': private
  '@vue/babel-helper-vue-transform-on@1.4.0':
    '@vue/babel-helper-vue-transform-on': private
  '@vue/babel-plugin-jsx@1.4.0(@babel/core@7.27.1)':
    '@vue/babel-plugin-jsx': private
  '@vue/babel-plugin-resolve-type@1.4.0(@babel/core@7.27.1)':
    '@vue/babel-plugin-resolve-type': private
  '@vue/compiler-core@3.5.14':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.14':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.14':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.14':
    '@vue/compiler-ssr': private
  '@vue/compiler-vue2@2.7.16':
    '@vue/compiler-vue2': private
  '@vue/devtools-api@7.7.6':
    '@vue/devtools-api': private
  '@vue/devtools-core@7.7.6(vite@6.3.5(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(stylus@0.57.0)(yaml@2.8.0))(vue@3.5.14(typescript@5.8.3))':
    '@vue/devtools-core': private
  '@vue/devtools-kit@7.7.6':
    '@vue/devtools-kit': private
  '@vue/devtools-shared@7.7.6':
    '@vue/devtools-shared': private
  '@vue/language-core@2.2.10(typescript@5.8.3)':
    '@vue/language-core': private
  '@vue/reactivity@3.5.14':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.14':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.14':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.14(vue@3.5.14(typescript@5.8.3))':
    '@vue/server-renderer': private
  '@vue/shared@3.5.14':
    '@vue/shared': private
  '@vuedx/template-ast-types@0.7.1':
    '@vuedx/template-ast-types': private
  '@vueuse/core@12.8.2(typescript@5.8.3)':
    '@vueuse/core': private
  '@vueuse/metadata@12.8.2':
    '@vueuse/metadata': private
  '@vueuse/shared@12.8.2(typescript@5.8.3)':
    '@vueuse/shared': private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn@8.14.1:
    acorn: private
  ajv@8.17.1:
    ajv: private
  alien-signals@1.0.13:
    alien-signals: private
  ansi-regex@6.1.0:
    ansi-regex: private
  ansi-styles@6.2.1:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.6:
    aria-hidden: private
  assertion-error@2.0.1:
    assertion-error: private
  ast-types@0.14.2:
    ast-types: private
  astral-regex@2.0.0:
    astral-regex: private
  atob@2.1.2:
    atob: private
  balanced-match@1.0.2:
    balanced-match: private
  binary-extensions@2.3.0:
    binary-extensions: private
  birpc@2.3.0:
    birpc: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@2.0.1:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.24.5:
    browserslist: private
  bundle-name@4.1.0:
    bundle-name: private
  cac@6.7.14:
    cac: private
  callsites@3.1.0:
    callsites: private
  camelcase-css@2.0.1:
    camelcase-css: private
  caniuse-lite@1.0.30001718:
    caniuse-lite: private
  chai@5.2.0:
    chai: private
  chalk@5.4.1:
    chalk: private
  check-error@2.1.1:
    check-error: private
  chokidar@3.6.0:
    chokidar: private
  chownr@3.0.0:
    chownr: private
  citty@0.1.6:
    citty: private
  cli-cursor@5.0.0:
    cli-cursor: private
  cli-progress@3.12.0:
    cli-progress: private
  cli-spinners@2.9.2:
    cli-spinners: private
  code-block-writer@13.0.3:
    code-block-writer: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  commander@12.1.0:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.1.8:
    confbox: private
  consola@3.4.2:
    consola: private
  convert-source-map@2.0.0:
    convert-source-map: private
  copy-anything@3.0.5:
    copy-anything: private
  cosmiconfig@9.0.0(typescript@5.8.3):
    cosmiconfig: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-select@5.1.0:
    css-select: private
  css-what@6.1.0:
    css-what: private
  css@3.0.0:
    css: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  de-indent@1.0.2:
    de-indent: private
  debug@4.4.1:
    debug: private
  decode-uri-component@0.2.2:
    decode-uri-component: private
  deep-diff@1.0.2:
    deep-diff: private
  deep-eql@5.0.2:
    deep-eql: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  default-browser-id@5.0.0:
    default-browser-id: private
  default-browser@5.2.1:
    default-browser: private
  define-lazy-prop@3.0.0:
    define-lazy-prop: private
  defu@6.1.4:
    defu: private
  destr@2.0.5:
    destr: private
  detect-libc@2.0.4:
    detect-libc: private
  didyoumean@1.2.2:
    didyoumean: private
  diff@7.0.0:
    diff: private
  dlv@1.1.3:
    dlv: private
  dom-serializer@2.0.0:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@5.0.3:
    domhandler: private
  domutils@3.2.2:
    domutils: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  electron-to-chromium@1.5.157:
    electron-to-chromium: private
  emoji-regex@10.4.0:
    emoji-regex: private
  enhanced-resolve@5.18.1:
    enhanced-resolve: private
  entities@4.5.0:
    entities: private
  env-paths@2.2.1:
    env-paths: private
  error-ex@1.3.2:
    error-ex: private
  error-stack-parser-es@0.1.5:
    error-stack-parser-es: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  esbuild@0.25.4:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@7.2.2:
    eslint-scope: public
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: public
  eslint@9.27.0(jiti@2.4.2):
    eslint: public
  espree@9.6.1:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  execa@9.5.3:
    execa: private
  expect-type@1.2.1:
    expect-type: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.3.0:
    fast-diff: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-uri@3.0.6:
    fast-uri: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.4(picomatch@4.0.2):
    fdir: private
  fflate@0.8.2:
    fflate: private
  figures@6.1.0:
    figures: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  foreground-child@3.3.1:
    foreground-child: private
  fraction.js@4.3.7:
    fraction.js: private
  fs-extra@11.3.0:
    fs-extra: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-east-asian-width@1.3.0:
    get-east-asian-width: private
  get-own-enumerable-keys@1.0.0:
    get-own-enumerable-keys: private
  get-stream@9.0.1:
    get-stream: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@7.2.3:
    glob: private
  globals@11.12.0:
    globals: private
  gonzales-pe@4.3.0:
    gonzales-pe: private
  graceful-fs@4.2.11:
    graceful-fs: private
  has-flag@4.0.0:
    has-flag: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  hookable@5.5.3:
    hookable: private
  human-signals@8.0.1:
    human-signals: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-core-module@2.16.1:
    is-core-module: private
  is-docker@3.0.0:
    is-docker: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-inside-container@1.0.0:
    is-inside-container: private
  is-interactive@2.0.0:
    is-interactive: private
  is-number@7.0.0:
    is-number: private
  is-obj@3.0.0:
    is-obj: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-regexp@3.1.0:
    is-regexp: private
  is-stream@4.0.1:
    is-stream: private
  is-unicode-supported@2.1.0:
    is-unicode-supported: private
  is-what@4.1.16:
    is-what: private
  is-wsl@3.1.0:
    is-wsl: private
  isexe@3.1.1:
    isexe: private
  jackspeak@4.1.1:
    jackspeak: private
  jiti@2.4.2:
    jiti: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@4.0.0:
    json-parse-even-better-errors: private
  json-schema-traverse@1.0.0:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jsonfile@6.1.0:
    jsonfile: private
  keyv@4.5.4:
    keyv: private
  kleur@3.0.3:
    kleur: private
  kolorist@1.8.0:
    kolorist: private
  levn@0.4.1:
    levn: private
  lightningcss-darwin-arm64@1.30.1:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.30.1:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.30.1:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.30.1:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.30.1:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.30.1:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  locate-path@6.0.0:
    locate-path: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.sortedlastindex@4.1.0:
    lodash.sortedlastindex: private
  lodash.truncate@4.4.2:
    lodash.truncate: private
  lodash@4.17.21:
    lodash: private
  log-symbols@6.0.0:
    log-symbols: private
  loupe@3.1.3:
    loupe: private
  lru-cache@5.1.1:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  memorystream@0.3.1:
    memorystream: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mimic-function@5.0.1:
    mimic-function: private
  minimatch@9.0.5:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mitt@3.0.1:
    mitt: private
  mkdirp@3.0.1:
    mkdirp: private
  mlly@1.7.4:
    mlly: private
  mrmime@2.0.1:
    mrmime: private
  ms@2.1.3:
    ms: private
  muggle-string@0.4.1:
    muggle-string: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  node-fetch-native@1.6.6:
    node-fetch-native: private
  node-html-parser@6.1.13:
    node-html-parser: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  npm-normalize-package-bin@4.0.0:
    npm-normalize-package-bin: private
  npm-run-path@6.0.0:
    npm-run-path: private
  nth-check@2.1.1:
    nth-check: private
  nypm@0.5.4:
    nypm: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  ofetch@1.4.1:
    ofetch: private
  ohash@2.0.11:
    ohash: private
  once@1.4.0:
    once: private
  onetime@7.0.0:
    onetime: private
  open@10.1.2:
    open: private
  optionator@0.9.4:
    optionator: private
  ora@8.2.0:
    ora: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  parse-ms@4.0.0:
    parse-ms: private
  path-browserify@1.0.1:
    path-browserify: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@2.0.0:
    path-scurry: private
  pathe@2.0.3:
    pathe: private
  pathval@2.0.0:
    pathval: private
  perfect-debounce@1.0.0:
    perfect-debounce: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pidtree@0.6.0:
    pidtree: private
  pify@2.3.0:
    pify: private
  pirates@4.0.7:
    pirates: private
  pkg-types@1.3.1:
    pkg-types: private
  postcss-import@15.1.0(postcss@8.5.3):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.5.3):
    postcss-js: private
  postcss-less@6.0.0(postcss@8.5.3):
    postcss-less: private
  postcss-load-config@4.0.2(postcss@8.5.3):
    postcss-load-config: private
  postcss-nested@6.2.0(postcss@8.5.3):
    postcss-nested: private
  postcss-sass@0.5.0:
    postcss-sass: private
  postcss-scss@4.0.9(postcss@8.5.3):
    postcss-scss: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-styl@0.12.3:
    postcss-styl: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier@3.5.3:
    prettier: public
  pretty-ms@9.2.0:
    pretty-ms: private
  prompts@2.4.2:
    prompts: private
  punycode@2.3.1:
    punycode: private
  queue-microtask@1.2.3:
    queue-microtask: private
  read-cache@1.0.0:
    read-cache: private
  read-package-json-fast@4.0.0:
    read-package-json-fast: private
  readdirp@3.6.0:
    readdirp: private
  recast@0.23.11:
    recast: private
  reka-ui@2.2.1(typescript@5.8.3)(vue@3.5.14(typescript@5.8.3)):
    reka-ui: private
  require-from-string@2.0.2:
    require-from-string: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.10:
    resolve: private
  restore-cursor@5.1.0:
    restore-cursor: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  rollup@4.41.0:
    rollup: private
  run-applescript@7.0.0:
    run-applescript: private
  run-parallel@1.2.0:
    run-parallel: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sax@1.2.4:
    sax: private
  semver@6.3.1:
    semver: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.2:
    shell-quote: private
  siginfo@2.0.0:
    siginfo: private
  signal-exit@4.1.0:
    signal-exit: private
  sirv@3.0.1:
    sirv: private
  sisteransi@1.0.5:
    sisteransi: private
  slice-ansi@4.0.0:
    slice-ansi: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-resolve@0.6.0:
    source-map-resolve: private
  source-map@0.7.4:
    source-map: private
  speakingurl@14.0.1:
    speakingurl: private
  stackback@0.0.2:
    stackback: private
  std-env@3.9.0:
    std-env: private
  stdin-discarder@0.2.2:
    stdin-discarder: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@7.2.0:
    string-width: private
  stringify-object@5.0.0:
    stringify-object: private
  strip-ansi@6.0.1:
    strip-ansi-cjs: private
  strip-ansi@7.1.0:
    strip-ansi: private
  strip-final-newline@4.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  stylus@0.57.0:
    stylus: private
  sucrase@3.35.0:
    sucrase: private
  superjson@2.2.2:
    superjson: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  table@6.9.0:
    table: private
  tapable@2.2.2:
    tapable: private
  tar@7.4.3:
    tar: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  tinybench@2.9.0:
    tinybench: private
  tinyexec@0.3.2:
    tinyexec: private
  tinyglobby@0.2.13:
    tinyglobby: private
  tinypool@1.0.2:
    tinypool: private
  tinyrainbow@2.0.0:
    tinyrainbow: private
  tinyspy@3.0.2:
    tinyspy: private
  to-regex-range@5.0.1:
    to-regex-range: private
  totalist@3.0.1:
    totalist: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  ts-morph@24.0.0:
    ts-morph: private
  tslib@2.8.1:
    tslib: private
  type-check@0.4.0:
    type-check: private
  ufo@1.6.1:
    ufo: private
  undici-types@6.21.0:
    undici-types: private
  undici@7.10.0:
    undici: private
  unicorn-magic@0.3.0:
    unicorn-magic: private
  universalify@2.0.1:
    universalify: private
  update-browserslist-db@1.1.3(browserslist@4.24.5):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  util-deprecate@1.0.2:
    util-deprecate: private
  vite-hot-client@2.0.4(vite@6.3.5(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(stylus@0.57.0)(yaml@2.8.0)):
    vite-hot-client: private
  vite-node@3.1.4(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(stylus@0.57.0)(yaml@2.8.0):
    vite-node: private
  vite-plugin-inspect@0.8.9(rollup@4.41.0)(vite@6.3.5(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(stylus@0.57.0)(yaml@2.8.0)):
    vite-plugin-inspect: private
  vite-plugin-vue-inspector@5.3.1(vite@6.3.5(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(stylus@0.57.0)(yaml@2.8.0)):
    vite-plugin-vue-inspector: private
  vitest@3.1.4(@types/node@22.15.21)(@vitest/ui@3.1.4)(jiti@2.4.2)(lightningcss@1.30.1)(stylus@0.57.0)(yaml@2.8.0):
    vitest: private
  vscode-uri@3.1.0:
    vscode-uri: private
  vue-demi@0.14.10(vue@3.5.14(typescript@5.8.3)):
    vue-demi: private
  vue-eslint-parser@9.4.3(eslint@9.27.0(jiti@2.4.2)):
    vue-eslint-parser: public
  vue-metamorph@3.2.0(eslint@9.27.0(jiti@2.4.2)):
    vue-metamorph: private
  which@5.0.0:
    which: private
  why-is-node-running@2.3.0:
    why-is-node-running: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  yallist@5.0.0:
    yallist: private
  yaml@2.8.0:
    yaml: private
  yocto-queue@0.1.0:
    yocto-queue: private
  yoctocolors@2.1.1:
    yoctocolors: private
  zod@3.25.23:
    zod: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.12.1
pendingBuilds: []
prunedAt: Fri, 23 May 2025 04:20:05 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.4'
  - '@esbuild/android-arm64@0.25.4'
  - '@esbuild/android-arm@0.25.4'
  - '@esbuild/android-x64@0.25.4'
  - '@esbuild/darwin-arm64@0.25.4'
  - '@esbuild/darwin-x64@0.25.4'
  - '@esbuild/freebsd-arm64@0.25.4'
  - '@esbuild/freebsd-x64@0.25.4'
  - '@esbuild/linux-arm64@0.25.4'
  - '@esbuild/linux-arm@0.25.4'
  - '@esbuild/linux-ia32@0.25.4'
  - '@esbuild/linux-loong64@0.25.4'
  - '@esbuild/linux-mips64el@0.25.4'
  - '@esbuild/linux-ppc64@0.25.4'
  - '@esbuild/linux-riscv64@0.25.4'
  - '@esbuild/linux-s390x@0.25.4'
  - '@esbuild/linux-x64@0.25.4'
  - '@esbuild/netbsd-arm64@0.25.4'
  - '@esbuild/netbsd-x64@0.25.4'
  - '@esbuild/openbsd-arm64@0.25.4'
  - '@esbuild/openbsd-x64@0.25.4'
  - '@esbuild/sunos-x64@0.25.4'
  - '@esbuild/win32-ia32@0.25.4'
  - '@esbuild/win32-x64@0.25.4'
  - '@rollup/rollup-android-arm-eabi@4.41.0'
  - '@rollup/rollup-android-arm64@4.41.0'
  - '@rollup/rollup-darwin-arm64@4.41.0'
  - '@rollup/rollup-darwin-x64@4.41.0'
  - '@rollup/rollup-freebsd-arm64@4.41.0'
  - '@rollup/rollup-freebsd-x64@4.41.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.41.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.41.0'
  - '@rollup/rollup-linux-arm64-gnu@4.41.0'
  - '@rollup/rollup-linux-arm64-musl@4.41.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.41.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.41.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.41.0'
  - '@rollup/rollup-linux-riscv64-musl@4.41.0'
  - '@rollup/rollup-linux-s390x-gnu@4.41.0'
  - '@rollup/rollup-linux-x64-gnu@4.41.0'
  - '@rollup/rollup-linux-x64-musl@4.41.0'
  - '@rollup/rollup-win32-ia32-msvc@4.41.0'
  - '@rollup/rollup-win32-x64-msvc@4.41.0'
  - '@tailwindcss/oxide-android-arm64@4.1.8'
  - '@tailwindcss/oxide-darwin-arm64@4.1.8'
  - '@tailwindcss/oxide-darwin-x64@4.1.8'
  - '@tailwindcss/oxide-freebsd-x64@4.1.8'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.8'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.8'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.8'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.8'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.8'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.8'
  - '@tailwindcss/oxide-win32-x64-msvc@4.1.8'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-x64-msvc@1.30.1
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v3
virtualStoreDir: C:\Users\<USER>\Projects\go\csee\etl\portal\frontend\node_modules\.pnpm
virtualStoreDirMaxLength: 120
