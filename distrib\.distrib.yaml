email_server:
    credential: ZW5jLzA6YjRtNnFabjZWY0EzNFNiZitKNE5pcGpLemp4UElkRDlVbVdaNFYwRXVCaVV4UmZQMWdKVjNsd1EzMnNvTitQQS9WdkxSVlRTa1VyWFZFTT0=
    host: ************
    port: 25
    sender_address: <EMAIL>
    sender_name: BPCS
error_notif_recipients:
    - ziska.<PERSON><PERSON><PERSON><PERSON>@ioh.co.id
gpfat:
    credential: ZW5jLzE1Ok1JSVRydnR0WXpTd2JEMTJqL2svaWMzU2xwM2NLdVp1UDV3OWlLRlRKb0k0MDd6d3hYck03WTlMMTNKNVIxNU9MU0lOV2JnPQ==
    database: pdwh
    host: localhost
    port: 2345
impala:
    ca_cert: truststore.jks
    credential: ZW5jLzIwOlBRNTZDK1lFM3VSM1NlNmppQ1NnRVA2cGMycS9qU1p5Wk5UTmtEa0s3cCsxVk02MWdZb056LzZDMm9YWks0K2w5dz09
    database: default
    host: udc2-impala-lb.office.corp.indosat.com
    port: 21051
log_level: 0
report_recipients:
    - dmin
tunnel:
    destination: ************:5432
    local_port: 2345
    ssh:
        host: ************
        port: 22
        private_key_file: C:\Users\<USER>\.ssh\id_rsa
        user: "80208379"
work_dir: workdir
