{"name": "vue-metamorph", "version": "3.2.0", "description": "Codemod framework for Vue files", "keywords": ["codemod", "vue", "ast"], "bin": {"vue-metamorph": "scripts/scaffold.js"}, "repository": {"type": "git", "url": "https://github.com/UnrefinedBrain/vue-metamorph.git"}, "bugs": "https://github.com/UnrefinedBrain/vue-metamorph/issues", "author": {"name": "UnrefinedBrain", "email": "<EMAIL>", "url": "https://github.com/UnrefinedBrain"}, "license": "MIT", "files": ["dist", "script/scaffold.js", "template"], "type": "module", "main": "dist/main.js", "types": "dist/vue-metamorph.d.ts", "typings": "dist/vue-metamorph.d.ts", "exports": {".": {"import": "./dist/main.js", "types": "./dist/vue-metamorph.d.ts"}}, "scripts": {"build": "scripts/build", "lint": "eslint src docs/.vitepress --ignore-path .gitignore", "test": "vitest", "docs:dev": "pnpm build && vitepress dev docs", "docs:build": "pnpm build && vitepress build docs", "docs:preview": "vitepress preview docs"}, "devDependencies": {"@babel/types": "^7.24.7", "@microsoft/api-documenter": "^7.25.3", "@microsoft/api-extractor": "^7.47.0", "@shikijs/vitepress-twoslash": "^1.8.0", "@types/cli-progress": "^3.11.5", "@types/deep-diff": "^1.0.5", "@types/lodash-es": "^4.17.12", "@types/micromatch": "^4.0.9", "@types/node": "^22.0.0", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "@vitest/coverage-v8": "^2.0.0", "eslint": "^8.57.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^18.0.0", "rollup-plugin-visualizer": "^5.12.0", "tsup": "^8.1.0", "typescript": "^5.5.2", "vitepress": "1.3.4", "vitest": "^2.0.0", "vue-metamorph": "link:"}, "dependencies": {"@babel/parser": "8.0.0-alpha.12", "ast-types": "^0.14.2", "chalk": "^5.3.0", "cli-progress": "^3.12.0", "commander": "^12.1.0", "deep-diff": "^1.0.2", "fs-extra": "^11.2.0", "glob": "^11.0.0", "lodash-es": "^4.17.21", "magic-string": "^0.30.10", "micromatch": "^4.0.8", "node-html-parser": "^6.1.13", "postcss": "^8.4.38", "postcss-less": "^6.0.0", "postcss-sass": "^0.5.0", "postcss-scss": "^4.0.9", "postcss-styl": "^0.12.3", "recast": "^0.23.9", "table": "^6.8.2", "vue-eslint-parser": "^9.4.3"}}