@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Projects\go\csee\etl\portal\frontend\node_modules\.pnpm\vue-metamorph@3.2.0_eslint@9.27.0_jiti@2.4.2_\node_modules\vue-metamorph\scripts\node_modules;C:\Users\<USER>\Projects\go\csee\etl\portal\frontend\node_modules\.pnpm\vue-metamorph@3.2.0_eslint@9.27.0_jiti@2.4.2_\node_modules\vue-metamorph\node_modules;C:\Users\<USER>\Projects\go\csee\etl\portal\frontend\node_modules\.pnpm\vue-metamorph@3.2.0_eslint@9.27.0_jiti@2.4.2_\node_modules;C:\Users\<USER>\Projects\go\csee\etl\portal\frontend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Projects\go\csee\etl\portal\frontend\node_modules\.pnpm\vue-metamorph@3.2.0_eslint@9.27.0_jiti@2.4.2_\node_modules\vue-metamorph\scripts\node_modules;C:\Users\<USER>\Projects\go\csee\etl\portal\frontend\node_modules\.pnpm\vue-metamorph@3.2.0_eslint@9.27.0_jiti@2.4.2_\node_modules\vue-metamorph\node_modules;C:\Users\<USER>\Projects\go\csee\etl\portal\frontend\node_modules\.pnpm\vue-metamorph@3.2.0_eslint@9.27.0_jiti@2.4.2_\node_modules;C:\Users\<USER>\Projects\go\csee\etl\portal\frontend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\scripts\scaffold.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\scripts\scaffold.js" %*
)
