@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Projects\go\csee\etl\portal\frontend\node_modules\.pnpm\shadcn-vue@2.1.0_@vitest+ui@3.1.4_eslint@9.27.0_jiti@2.4.2__typescript@5.8.3_vitest@3.1.4_vue@3.5.14_typescript@5.8.3_\node_modules\shadcn-vue\dist\node_modules;C:\Users\<USER>\Projects\go\csee\etl\portal\frontend\node_modules\.pnpm\shadcn-vue@2.1.0_@vitest+ui@3.1.4_eslint@9.27.0_jiti@2.4.2__typescript@5.8.3_vitest@3.1.4_vue@3.5.14_typescript@5.8.3_\node_modules\shadcn-vue\node_modules;C:\Users\<USER>\Projects\go\csee\etl\portal\frontend\node_modules\.pnpm\shadcn-vue@2.1.0_@vitest+ui@3.1.4_eslint@9.27.0_jiti@2.4.2__typescript@5.8.3_vitest@3.1.4_vue@3.5.14_typescript@5.8.3_\node_modules;C:\Users\<USER>\Projects\go\csee\etl\portal\frontend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Projects\go\csee\etl\portal\frontend\node_modules\.pnpm\shadcn-vue@2.1.0_@vitest+ui@3.1.4_eslint@9.27.0_jiti@2.4.2__typescript@5.8.3_vitest@3.1.4_vue@3.5.14_typescript@5.8.3_\node_modules\shadcn-vue\dist\node_modules;C:\Users\<USER>\Projects\go\csee\etl\portal\frontend\node_modules\.pnpm\shadcn-vue@2.1.0_@vitest+ui@3.1.4_eslint@9.27.0_jiti@2.4.2__typescript@5.8.3_vitest@3.1.4_vue@3.5.14_typescript@5.8.3_\node_modules\shadcn-vue\node_modules;C:\Users\<USER>\Projects\go\csee\etl\portal\frontend\node_modules\.pnpm\shadcn-vue@2.1.0_@vitest+ui@3.1.4_eslint@9.27.0_jiti@2.4.2__typescript@5.8.3_vitest@3.1.4_vue@3.5.14_typescript@5.8.3_\node_modules;C:\Users\<USER>\Projects\go\csee\etl\portal\frontend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\dist\index.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\dist\index.js" %*
)
