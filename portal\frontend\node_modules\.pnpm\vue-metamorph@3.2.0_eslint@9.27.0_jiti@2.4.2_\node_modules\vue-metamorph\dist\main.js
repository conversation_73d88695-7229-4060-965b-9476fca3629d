var __defProp = Object.defineProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};

// src/main.ts
import { builders as scriptBuilders } from "ast-types";
import postcss2 from "postcss";

// src/builders.ts
var builders_exports = {};
__export(builders_exports, {
  setParents: () => setParents,
  vAttribute: () => vAttribute,
  vDirective: () => vDirective,
  vDirectiveKey: () => vDirectiveKey,
  vDocumentFragment: () => vDocumentFragment,
  vElement: () => vElement,
  vEndTag: () => vEndTag,
  vExpressionContainer: () => vExpressionContainer,
  vFilter: () => vFilter,
  vFilterSequenceExpression: () => vFilterSequenceExpression,
  vForExpression: () => vForExpression,
  vIdentifier: () => vIdentifier,
  vLiteral: () => vLiteral,
  vOnExpression: () => vOnExpression,
  vStartTag: () => vStartTag,
  vText: () => vText
});

// src/stringify.ts
import * as recast from "recast";
var voidElements = {
  area: true,
  base: true,
  br: true,
  col: true,
  command: true,
  embed: true,
  hr: true,
  img: true,
  input: true,
  keygen: true,
  link: true,
  meta: true,
  param: true,
  source: true,
  track: true,
  wbr: true
};
function stringifyWithRecast(node) {
  return recast.prettyPrint(node, {
    quote: "single",
    tabWidth: 2,
    trailingComma: true
  }).code;
}
function stringifyVIdentifier(node) {
  return node.rawName;
}
var shorthands = {
  bind: ":",
  on: "@",
  slot: "#",
  generic: "generic"
};
function stringifyVDirectiveKey(node) {
  let str = "";
  let shorthand = false;
  if (shorthands[node.name.name] && shorthands[node.name.name] === node.name.rawName) {
    shorthand = true;
    str += node.name.rawName;
  } else {
    str += `v-${stringifyVIdentifier(node.name)}`;
  }
  if (node.argument) {
    if (!shorthand) {
      str += ":";
    }
    switch (node.argument.type) {
      case "VExpressionContainer": {
        str += "[";
        str += stringifyVExpressionContainer(node.argument);
        str += "]";
        break;
      }
      case "VIdentifier": {
        str += stringifyVIdentifier(node.argument);
        break;
      }
      // @ts-expect-error We shouldn't hit this case if the types are correct
      default:
        throw new Error(`Unexpected argument type: ${node.argument.type}`);
    }
  }
  if (node.modifiers.length > 0) {
    for (const modifier of node.modifiers) {
      str += `.${stringifyVIdentifier(modifier)}`;
    }
  }
  return str;
}
function stringifyVLiteral(node) {
  return node.value;
}
function stringifyVAttribute(node) {
  let str = node.directive ? stringifyVDirectiveKey(node.key) : node.key.rawName;
  if (node.value) {
    str += `="${stringify(node.value)}"`;
  }
  return str;
}
function stringifyVStartTag(node, isVoidElement = false) {
  let str = "";
  for (const attribute of node.attributes) {
    str += ` ${stringifyVAttribute(attribute)}`;
  }
  if (node.selfClosing && !isVoidElement) {
    str += " /";
  }
  return str;
}
function stringifyVEndTag(_node) {
  return "";
}
function stringifyVElement(node) {
  let str = `<${node.rawName}`;
  str += stringifyVStartTag(node.startTag, voidElements[node.rawName] ?? false);
  str += ">";
  if (!node.startTag.selfClosing && !voidElements[node.rawName]) {
    for (const child of node.children) {
      if (child.type === "VExpressionContainer") {
        str += "{{ ";
      }
      str += stringify(child);
      if (child.type === "VExpressionContainer") {
        str += " }}";
      }
    }
    str += `</${node.rawName}>`;
  }
  return str;
}
function stringifyVExpressionContainer(node) {
  if (!node.expression) {
    return "";
  }
  if (node.expression.type === "VSlotScopeExpression" || node.expression.type === "VForExpression" || node.expression.type === "VOnExpression" || node.expression.type === "VFilterSequenceExpression" || node.expression.type === "VGenericExpression") {
    return stringify(node.expression);
  }
  return stringifyWithRecast(node.expression);
}
function stringifyVFilterSequenceExpression(node) {
  let str = stringifyWithRecast(node.expression);
  for (const filter of node.filters) {
    str += ` | ${stringifyWithRecast(filter.callee)}`;
    if (filter.arguments.length) {
      str += "(";
      str += filter.arguments.map(stringifyWithRecast).join(", ");
      str += ")";
    }
  }
  return str;
}
function stringifyVForExpression(node) {
  let str = "";
  const multiple = node.left.length > 1;
  if (multiple) {
    str += "(";
  }
  str += node.left.map(stringifyWithRecast).join(", ");
  if (multiple) {
    str += ")";
  }
  str += ` in ${stringifyWithRecast(node.right)}`;
  return str;
}
function stringifyVOnExpression(node) {
  return node.body[0] ? stringifyWithRecast(node.body[0]) : "";
}
function stringifyVSlotScopeExpression(node) {
  return node.params[0] ? stringifyWithRecast(node.params[0]) : "";
}
function stringifyVText(node) {
  return node.value;
}
function stringifyVDocumentFragment(node) {
  return node.children.map(stringify).join("");
}
function stringifyVGenericExpression(node) {
  return node.params.map(stringifyWithRecast).join(", ");
}
function stringify(node) {
  switch (node.type) {
    case "VAttribute":
      return stringifyVAttribute(node);
    case "VDirectiveKey":
      return stringifyVDirectiveKey(node);
    case "VElement":
      return stringifyVElement(node);
    case "VEndTag":
      return stringifyVEndTag(node);
    case "VExpressionContainer":
      return stringifyVExpressionContainer(node);
    case "VIdentifier":
      return stringifyVIdentifier(node);
    case "VLiteral":
      return stringifyVLiteral(node);
    case "VStartTag":
      return stringifyVStartTag(node);
    case "VText":
      return stringifyVText(node);
    case "VForExpression":
      return stringifyVForExpression(node);
    case "VOnExpression":
      return stringifyVOnExpression(node);
    case "VSlotScopeExpression":
      return stringifyVSlotScopeExpression(node);
    case "VFilterSequenceExpression":
      return stringifyVFilterSequenceExpression(node);
    case "VDocumentFragment":
      return stringifyVDocumentFragment(node);
    case "VGenericExpression":
      return stringifyVGenericExpression(node);
    default:
      return stringifyWithRecast(node);
  }
}

// src/ast.ts
import { AST } from "vue-eslint-parser";
var NS = Object.freeze({
  HTML: "http://www.w3.org/1999/xhtml",
  MathML: "http://www.w3.org/1998/Math/MathML",
  SVG: "http://www.w3.org/2000/svg",
  XLink: "http://www.w3.org/1999/xlink",
  XML: "http://www.w3.org/XML/1998/namespace",
  XMLNS: "http://www.w3.org/2000/xmlns/"
});
var traverseNodes = (node, visitor) => {
  const noop = () => {
  };
  AST.traverseNodes(node, {
    enterNode: visitor.enterNode ?? noop,
    leaveNode: visitor.leaveNode ?? noop
  });
};

// src/builders.ts
function setParents(node) {
  traverseNodes(node, {
    enterNode(innerNode, parent) {
      innerNode.parent = parent;
    },
    leaveNode() {
    }
  });
}
function vAttribute(key, value) {
  return {
    type: "VAttribute",
    directive: false,
    key,
    value,
    // @ts-expect-error Parent is not known yet
    parent: void 0
  };
}
function vDirective(key, value) {
  return {
    type: "VAttribute",
    directive: true,
    key,
    value,
    // @ts-expect-error Parent is not known yet
    parent: void 0
  };
}
function vDirectiveKey(name, argument = null, modifiers = []) {
  return {
    type: "VDirectiveKey",
    name,
    argument,
    modifiers,
    // @ts-expect-error Parent is not known yet
    parent: void 0
  };
}
function vDocumentFragment(children) {
  return {
    type: "VDocumentFragment",
    children,
    // @ts-expect-error Parent is not known yet
    parent: void 0
  };
}
function vEndTag() {
  return {
    type: "VEndTag",
    // @ts-expect-error Parent is not known yet
    parent: void 0
  };
}
function vElement(name, startTag, children, namespace = "http://www.w3.org/1999/xhtml") {
  return {
    type: "VElement",
    name,
    rawName: name,
    children,
    startTag,
    namespace,
    // @ts-expect-error Parent is not known yet
    parent: void 0,
    endTag: startTag.selfClosing || voidElements[name] ? null : vEndTag()
  };
}
function vExpressionContainer(expression) {
  return {
    type: "VExpressionContainer",
    references: [],
    expression,
    // @ts-expect-error Parent is not known yet
    parent: void 0
  };
}
function vForExpression(left, right) {
  return {
    type: "VForExpression",
    left,
    right,
    // @ts-expect-error Parent is not known yet
    parent: void 0
  };
}
function vIdentifier(name, rawName = name) {
  return {
    type: "VIdentifier",
    name,
    rawName,
    // @ts-expect-error Parent is not known yet
    parent: void 0
  };
}
function vLiteral(value) {
  return {
    type: "VLiteral",
    // @ts-expect-error Parent is not known yet
    parent: void 0,
    value
  };
}
function vStartTag(attributes, selfClosing) {
  return {
    type: "VStartTag",
    attributes,
    // @ts-expect-error Parent is not known yet
    parent: void 0,
    selfClosing
  };
}
function vText(value) {
  return {
    type: "VText",
    // @ts-expect-error Parent is not known yet
    parent: void 0,
    value
  };
}
function vOnExpression(body) {
  return {
    type: "VOnExpression",
    // @ts-expect-error Parent is not known yet
    parent: void 0,
    body
  };
}
function vFilterSequenceExpression(expression, filters) {
  return {
    type: "VFilterSequenceExpression",
    // @ts-expect-error Parent is not known yet
    parent: void 0,
    expression,
    filters
  };
}
function vFilter(callee, args) {
  return {
    type: "VFilter",
    arguments: args,
    callee,
    // @ts-expect-error Parent is not known yet
    parent: void 0
  };
}

// src/main.ts
import {
  namedTypes as namedTypes3,
  visit as visit4
} from "ast-types";

// src/cli.ts
import { Command } from "commander";
import { globSync } from "glob";
import { promises as fs } from "fs";
import micromatch from "micromatch";

// src/transform.ts
import MagicString from "magic-string";
import {
  cloneDeep,
  get,
  uniqWith,
  isEqual
} from "lodash-es";
import * as recast4 from "recast";
import deepDiff from "deep-diff";

// src/types.ts
import { builders as builders2, visit as visit2 } from "ast-types";

// src/ast-helpers.ts
var ast_helpers_exports = {};
__export(ast_helpers_exports, {
  createDefaultImport: () => createDefaultImport,
  createNamedImport: () => createNamedImport,
  createNamespaceImport: () => createNamespaceImport,
  findAll: () => findAll,
  findFirst: () => findFirst,
  findImportDeclaration: () => findImportDeclaration,
  findVueComponentOptions: () => findVueComponentOptions
});
import { builders, visit } from "ast-types";
import { isMatch } from "lodash-es";
function findFirst(ast, matcher) {
  let matchingNode = null;
  if (ast.type.startsWith("V")) {
    traverseNodes(ast, {
      enterNode(node) {
        if (!matchingNode && isMatch(node, matcher)) {
          matchingNode = node;
        }
      },
      leaveNode() {
      }
    });
  } else {
    visit(ast, {
      visitNode(path) {
        if (!matchingNode && isMatch(path.node, matcher)) {
          matchingNode = path.node;
          return this.abort();
        }
        return this.traverse(path);
      }
    });
  }
  return matchingNode;
}
function findAll(ast, matcher) {
  const matchingNodes = [];
  if (ast.type.startsWith("V")) {
    traverseNodes(ast, {
      enterNode(node) {
        if (isMatch(node, matcher)) {
          matchingNodes.push(node);
        }
      }
    });
  } else {
    visit(ast, {
      visitNode(path) {
        if (isMatch(path.node, matcher)) {
          matchingNodes.push(path.node);
        }
        this.traverse(path);
      }
    });
  }
  return matchingNodes;
}
function findImportDeclaration(ast, moduleSpecifier) {
  return findFirst(ast, {
    type: "ImportDeclaration",
    source: {
      type: "Literal",
      value: moduleSpecifier
    }
  });
}
function createNamedImport(ast, moduleSpecifier, importName, localName = importName) {
  const decl = findImportDeclaration(ast, moduleSpecifier);
  const newSpecifier = builders.importSpecifier(
    builders.identifier(importName),
    importName !== localName ? builders.identifier(localName) : null
  );
  if (!decl) {
    ast.body.unshift(
      builders.importDeclaration(
        [newSpecifier],
        builders.literal(moduleSpecifier)
      )
    );
  } else if (decl && !decl.specifiers) {
    decl.specifiers = [newSpecifier];
  } else if (decl && decl.specifiers) {
    let found = false;
    for (const specifier of decl.specifiers) {
      if (specifier.type !== "ImportSpecifier") {
        continue;
      }
      if (specifier.imported.type !== "Identifier") {
        continue;
      }
      if (specifier.imported.name === importName && (localName === importName || specifier.local?.name === localName)) {
        found = true;
      }
    }
    if (!found) {
      decl.specifiers.push(newSpecifier);
    }
  }
}
function createDefaultImport(ast, moduleSpecifier, importName) {
  const decl = findImportDeclaration(ast, moduleSpecifier);
  const newSpecifier = builders.importDefaultSpecifier(
    builders.identifier(importName)
  );
  if (!decl) {
    ast.body.unshift(
      builders.importDeclaration(
        [newSpecifier],
        builders.literal(moduleSpecifier)
      )
    );
  } else if (decl && !decl.specifiers) {
    decl.specifiers = [newSpecifier];
  } else if (decl && decl.specifiers) {
    let found = false;
    for (const specifier of decl.specifiers) {
      if (specifier.type !== "ImportDefaultSpecifier") {
        continue;
      }
      if (!specifier.local || specifier.local.type !== "Identifier") {
        continue;
      }
      if (specifier.local.name === importName) {
        found = true;
      }
    }
    if (!found) {
      decl.specifiers.push(newSpecifier);
    }
  }
}
function createNamespaceImport(ast, moduleSpecifier, namespaceName) {
  const decl = findImportDeclaration(ast, moduleSpecifier);
  const newSpecifier = builders.importNamespaceSpecifier(
    builders.identifier(namespaceName)
  );
  if (!decl) {
    ast.body.unshift(
      builders.importDeclaration(
        [newSpecifier],
        builders.literal(moduleSpecifier)
      )
    );
  } else if (decl && !decl.specifiers) {
    decl.specifiers = [newSpecifier];
  } else if (decl && decl.specifiers) {
    let found = false;
    for (const specifier of decl.specifiers) {
      if (specifier.type !== "ImportNamespaceSpecifier") {
        continue;
      }
      if (!specifier.name || specifier.name.type !== "Identifier") {
        continue;
      }
      if (specifier.name.name === namespaceName) {
        found = true;
      }
    }
    if (!found) {
      decl.specifiers.push(newSpecifier);
    }
  }
}
function findVueComponentOptions(ast, isSfc) {
  const objects = [];
  visit(ast, {
    visitExportDefaultDeclaration(path) {
      if (isSfc && path.node.declaration.type === "ObjectExpression") {
        objects.push(path.node.declaration);
      }
      this.traverse(path);
    },
    visitCallExpression(path) {
      if (path.node.callee.type === "Identifier" && path.node.callee.name === "defineComponent" && path.node.arguments[0]?.type === "ObjectExpression") {
        objects.push(path.node.arguments[0]);
      }
      if (path.node.callee.type === "MemberExpression" && path.node.callee.object.type === "Identifier" && path.node.callee.property.type === "Identifier" && path.node.callee.object.name === "Vue" && ["extend", "component", "mixin"].includes(path.node.callee.property.name) && path.node.arguments[0]?.type === "ObjectExpression") {
        objects.push(path.node.arguments[0]);
      }
      this.traverse(path);
    },
    visitNewExpression(path) {
      if (path.node.callee.type === "Identifier" && path.node.callee.name === "Vue" && path.node.arguments[0]?.type === "ObjectExpression") {
        objects.push(path.node.arguments[0]);
      }
      this.traverse(path);
    }
  });
  return objects;
}

// src/types.ts
var utils = {
  traverseTemplateAST: traverseNodes,
  traverseScriptAST: visit2,
  builders: {
    ...builders_exports,
    ...builders2
  },
  astHelpers: ast_helpers_exports
};

// src/parse/typescript.ts
import { visit as visit3 } from "ast-types";
import * as babelParser from "@babel/parser";
import * as recast2 from "recast";
var babelOptions = (isJsx) => ({
  strictMode: false,
  allowImportExportEverywhere: true,
  allowReturnOutsideFunction: true,
  startLine: 1,
  tokens: true,
  ranges: true,
  sourceType: "module",
  plugins: [
    "asyncGenerators",
    "bigInt",
    "classPrivateMethods",
    "classPrivateProperties",
    "classProperties",
    "classStaticBlock",
    "decimal",
    "decorators-legacy",
    "doExpressions",
    "dynamicImport",
    "estree",
    "exportDefaultFrom",
    "exportNamespaceFrom",
    "functionBind",
    "functionSent",
    "importAssertions",
    "importMeta",
    "nullishCoalescingOperator",
    "numericSeparator",
    "objectRestSpread",
    "optionalCatchBinding",
    "optionalChaining",
    [
      "pipelineOperator",
      {
        proposal: "minimal"
      }
    ],
    [
      "recordAndTuple"
    ],
    "throwExpressions",
    "topLevelAwait",
    "typescript",
    "v8intrinsic",
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    ...isJsx ? ["jsx"] : []
  ]
});
var tsParser = (isJsx) => ({
  parse: (code) => babelParser.parse(code, babelOptions(isJsx)),
  parseForESLint: (code) => {
    const res = babelParser.parse(code, babelOptions(isJsx));
    visit3(res.program, {
      visitNode(path) {
        const node = path.node;
        if (!node.range) {
          node.range = [node.start, node.end];
        }
        this.traverse(path);
      }
    });
    res.tokens?.forEach((tok) => {
      tok.range = [tok.start, tok.end];
    });
    res.program.tokens = res.tokens;
    res.program.comments = res.comments;
    return {
      ast: res.program
    };
  }
});
function parseTs(code, isJsx) {
  const ast = recast2.parse(code, {
    parser: tsParser(isJsx)
  }).program;
  ast.isScriptSetup = false;
  return ast;
}

// src/parse/vue.ts
import * as vueParser from "vue-eslint-parser";
import * as recast3 from "recast";
import htmlParser from "node-html-parser";

// src/parse/css.ts
import postcss from "postcss";
import postcssLess from "postcss-less";
import postcssSass from "postcss-sass";
import postcssScss from "postcss-scss";
import postcssStyl from "postcss-styl";
var syntaxMap = {
  css: postcss,
  scss: postcssScss,
  less: postcssLess,
  sass: postcssSass,
  stylus: postcssStyl
};
var isSupportedLang = (str) => !!syntaxMap[str];
var getLangAttribute = (el) => el.startTag.attributes.find((attr) => !attr.directive && attr.key.rawName === "lang")?.value?.value ?? "css";
var getCssDialectForFilename = (filename) => {
  switch (true) {
    case filename.endsWith(".scss"):
      return "scss";
    case filename.endsWith(".sass"):
      return "sass";
    case filename.endsWith(".less"):
      return "less";
    case filename.endsWith(".css"):
      return "css";
    case filename.endsWith(".styl"):
      return "stylus";
    default:
      return null;
  }
};
var parseCss = (code, dialect) => (syntaxMap[dialect] ?? postcss).parse(code);

// src/parse/vue.ts
function parseVue(code) {
  if (!htmlParser.parse(code).querySelector("template")) {
    code += "\n<template></template>";
  }
  const sfcAST = vueParser.parse(code, {
    parser: tsParser(true),
    sourceType: "module"
  });
  const scripts = findAll(sfcAST.templateBody.parent, {
    type: "VElement",
    name: "script"
  });
  const styles = findAll(sfcAST.templateBody.parent, {
    type: "VElement",
    name: "style"
  });
  const scriptASTs = scripts.filter((el) => el.children.length > 0).map((el) => {
    const blankLines = "\n".repeat(el.loc.start.line - 1);
    const start = el.children[0]?.range[0];
    const end = el.children[0]?.range[1];
    const isJsx = el.startTag.attributes.some((attr) => !attr.directive && attr.key.rawName === "lang" && attr.value && ["jsx", "tsx"].includes(attr.value.value));
    const ast = recast3.parse(`/* METAMORPH_START */${blankLines}${code.slice(start, end)}`, {
      parser: tsParser(isJsx)
    }).program;
    ast.isScriptSetup = el.startTag.attributes.some((attr) => !attr.directive && attr.key.rawName === "setup");
    return ast;
  });
  const styleASTs = styles.filter((el) => el.children.length > 0 && isSupportedLang(getLangAttribute(el))).map((el) => {
    const blankLines = "\n".repeat(el.loc.start.line - 1);
    const start = el.children[0]?.range[0];
    const end = el.children.at(-1).range[1];
    const lang = getLangAttribute(el);
    return parseCss(`/* METAMORPH_START */${blankLines}${code.slice(start, end)}`, lang);
  });
  return {
    sfcAST,
    scriptASTs,
    styleASTs
  };
}

// src/transform.ts
var recastOptions = {
  tabWidth: 2,
  arrowParensAlways: true,
  quote: "single",
  trailingComma: true
};
var ignoreProperties = {
  parent: true,
  loc: true,
  range: true,
  variables: true,
  references: true
};
function transformVueFile(code, filename, codemods, opts) {
  const workingCode = code;
  const stats = [];
  const ms = new MagicString(workingCode);
  const { scriptASTs, sfcAST, styleASTs } = parseVue(workingCode);
  const templateAst = sfcAST.templateBody?.parent;
  const originalTemplate = cloneDeep(templateAst);
  for (const codemod of codemods) {
    const count = codemod.transform({
      scriptASTs,
      sfcAST: templateAst ?? null,
      styleASTs,
      filename,
      utils,
      opts
    });
    stats.push([codemod.name, count]);
  }
  if (templateAst && originalTemplate) {
    setParents(templateAst);
    let scriptIndex = 0;
    let styleIndex = 0;
    traverseNodes(templateAst, {
      enterNode(node) {
        if (node.type === "VElement" && node.name === "script" && node.parent === templateAst && scriptIndex < scriptASTs.length) {
          const newCode = recast4.print(scriptASTs[scriptIndex], recastOptions).code.replace(/\/\* METAMORPH_START \*\/(\r?\n)*/g, "\n");
          const text = `${newCode.startsWith("\n") ? "" : "\n"}${newCode}
`;
          if (node.children[0]?.type === "VText") {
            node.children[0].value = text;
          } else {
            node.children.unshift(
              vText(text)
            );
          }
          scriptIndex++;
        }
        if (node.type === "VElement" && node.name === "style" && node.parent === templateAst && isSupportedLang(getLangAttribute(node)) && node.children[0]?.type === "VText") {
          const newCode = styleASTs[styleIndex].toString(syntaxMap[getLangAttribute(node)].stringify).replace(/\/\* METAMORPH_START \*\/(\r?\n)*/g, "\n");
          node.children.length = 0;
          node.children.push(vText(`${newCode.startsWith("\n") ? "" : "\n"}${newCode}`));
          styleIndex++;
        }
      },
      leaveNode() {
      }
    });
    const diff = deepDiff(
      originalTemplate,
      templateAst,
      (_, name) => !!ignoreProperties[name]
    );
    if (diff) {
      let rootNodeChanged = false;
      const changedNodes = diff.map((p) => {
        const path = [...p.path ?? []];
        path.pop();
        if (path.at(-1) === "startTag") {
          path.pop();
        }
        if (path.at(-2) === "key") {
          path.pop();
        }
        if (path.at(-1) === "expression") {
          path.pop();
          path.pop();
        }
        if (path.at(-1) === "body") {
          path.pop();
        }
        if (path.at(-1) === "children") {
          path.pop();
        }
        if (path.length <= 3 && p.kind !== "E") {
          rootNodeChanged = true;
        }
        const originalNode = rootNodeChanged ? originalTemplate : get(originalTemplate, path);
        return {
          path,
          start: originalNode.range[0],
          end: originalNode.range[1],
          node: path.length === 0 ? templateAst : get(templateAst, path)
        };
      });
      if (rootNodeChanged) {
        const [start, end] = originalTemplate.range;
        ms.update(start, end, stringify(templateAst));
      } else {
        const collapsedChanges = uniqWith(changedNodes, (a, b) => {
          if (a.path.length === b.path.length) {
            return isEqual(a.path, b.path);
          }
          const lesser = a.path.length < b.path.length ? a : b;
          const greater = lesser === a ? b : a;
          return isEqual(lesser.path, greater.path.slice(0, lesser.path.length - 1));
        }).sort((a, b) => b.path.length - a.path.length);
        for (const { start, end, node } of collapsedChanges) {
          ms.update(start, end, stringify(node));
        }
      }
    }
  }
  return {
    code: ms.toString(),
    stats
  };
}
function transformTypescriptFile(code, filename, codemods, opts) {
  const ast = parseTs(code, /\.[jt]sx$/.test(filename));
  const stats = [];
  for (const codemod of codemods) {
    const count = codemod.transform({
      scriptASTs: [ast],
      sfcAST: null,
      styleASTs: [],
      filename,
      utils,
      opts
    });
    stats.push([codemod.name, count]);
  }
  return {
    code: `${recast4.print(ast, recastOptions).code}
`,
    stats
  };
}
function transformCssFile(code, filename, codemods, opts) {
  const dialect = getCssDialectForFilename(filename);
  if (!dialect) {
    return {
      code,
      stats: []
    };
  }
  const ast = parseCss(code, dialect);
  const stats = [];
  for (const codemod of codemods) {
    const count = codemod.transform({
      scriptASTs: [],
      sfcAST: null,
      styleASTs: [ast],
      filename,
      utils,
      opts
    });
    stats.push([codemod.name, count]);
  }
  return {
    code: ast.toString(syntaxMap[dialect]),
    stats
  };
}
function transform(code, filename, plugins, opts = {}) {
  if (filename.endsWith(".vue")) {
    return transformVueFile(code, filename, plugins, opts);
  }
  if (getCssDialectForFilename(filename)) {
    return transformCssFile(code, filename, plugins, opts);
  }
  return transformTypescriptFile(code, filename, plugins, opts);
}

// src/manual.ts
var spaces = (n) => " ".repeat(n);
var carets = (n) => "^".repeat(n);
function sample({
  code,
  columnEnd,
  columnStart,
  lineEnd,
  lineStart,
  extraLines: extra
}) {
  const codeLines = code.split("\n");
  const firstLineNumber = Math.max(0, lineStart - extra - 1);
  const lastLineNumber = Math.min(codeLines.length, lineEnd + extra);
  const snippet = codeLines.slice(firstLineNumber, lastLineNumber);
  const prefix = (n) => `${String(n ?? "").padStart(String(lastLineNumber).length, " ")} | `;
  const lines = [];
  for (let i = 0; i < snippet.length; i++) {
    const line = snippet[i];
    const lineNumber = firstLineNumber + i + 1;
    lines.push(prefix(lineNumber) + line);
    if (lineNumber === lineStart && lineNumber === lineEnd) {
      lines.push(prefix() + spaces(columnStart - 1) + carets(columnEnd - columnStart + 1));
    } else if (lineNumber === lineStart) {
      lines.push(prefix() + spaces(columnStart - 1) + carets(line.length - columnStart + 1));
    } else if (lineNumber < lineEnd && lineNumber > lineStart) {
      lines.push(prefix() + carets(Math.max(line.length, 1)));
    } else if (lineNumber === lineEnd) {
      lines.push(prefix() + carets(columnEnd));
    }
  }
  return lines.join("\n");
}
function findManualMigrations(code, filename, plugins, opts = {}) {
  const reports = [];
  let scripts = [];
  let template = null;
  let styles = [];
  if (filename.endsWith(".vue")) {
    const { scriptASTs: scriptAsts, sfcAST: vueAst, styleASTs } = parseVue(code);
    scripts = scriptAsts;
    template = vueAst.templateBody.parent;
    styles = styleASTs;
  } else if (getCssDialectForFilename(filename)) {
    styles = [parseCss(code, getCssDialectForFilename(filename))];
  } else {
    scripts = [parseTs(code, /\.[jt]sx$/.test(filename))];
  }
  for (const plugin of plugins) {
    const report = (node, message) => {
      if ("loc" in node) {
        if (node.loc) {
          const snippet = sample({
            code,
            extraLines: 3,
            lineStart: node.loc.start.line,
            lineEnd: node.loc.end.line,
            columnStart: node.loc.start.column + 1,
            columnEnd: node.loc.end.column
          });
          reports.push({
            message,
            file: filename,
            snippet,
            pluginName: plugin.name,
            lineStart: node.loc.start.line,
            lineEnd: node.loc.end.line,
            columnStart: node.loc.start.column + 1,
            columnEnd: node.loc.end.column
          });
        } else if ("range" in node && Array.isArray(node.range)) {
          const [start, end] = node.range;
          const before = code.slice(0, start);
          const middle = code.slice(start, end);
          const lineStart = (before.match(/\n/g)?.length ?? 0) + 1;
          const columnStart = start - before.lastIndexOf("\n");
          const lineEnd = lineStart + (middle.match(/\n/g)?.length ?? 0);
          const columnEnd = end - code.slice(0, end).lastIndexOf("\n") - 1;
          const snippet = sample({
            code,
            extraLines: 3,
            lineStart,
            lineEnd,
            columnStart,
            columnEnd
          });
          reports.push({
            message,
            file: filename,
            snippet,
            pluginName: plugin.name,
            lineStart,
            lineEnd,
            columnStart,
            columnEnd
          });
        }
        return;
      }
      if ("positionInside" in node) {
        const { line: lineStart, column: columnStart } = node.source.start;
        const { line: lineEnd, column: columnEnd } = node.source.end;
        const snippet = sample({
          code,
          extraLines: 3,
          lineStart,
          lineEnd,
          columnStart,
          columnEnd: columnEnd - 1
        });
        reports.push({
          message,
          file: filename,
          snippet,
          pluginName: plugin.name,
          lineStart,
          lineEnd,
          columnStart,
          columnEnd: columnEnd - 1
        });
        return;
      }
      throw new Error(`Node type ${node.type} is missing location information`);
    };
    plugin.find({
      scriptASTs: scripts,
      sfcAST: template,
      styleASTs: styles,
      filename,
      report,
      utils,
      opts
    });
  }
  return reports;
}

// src/default-cli-progress-handler.ts
import cliProgress from "cli-progress";
import table from "table";
import chalk from "chalk";
var createDefaultCliProgressHandler = (console2) => {
  const bar = new cliProgress.SingleBar({
    format: "{bar} | {percentage}% | Processed {value} of {total} files. Errors: {errors}",
    hideCursor: true,
    fps: 60
  }, cliProgress.Presets.legacy);
  const defaultCliProgressHandler = ({
    aborted,
    done,
    filesProcessed,
    totalFiles,
    errors,
    manualMigrations,
    stats
  }) => {
    if (filesProcessed === 0) {
      bar.start(totalFiles, 0, { errors: 0 });
    }
    if (aborted || done) {
      bar.stop();
      console2.log(`Processed ${filesProcessed} of ${totalFiles} matching files with ${errors.length} errors`);
      if (errors.length > 0) {
        console2.log(chalk.bold.redBright("Parse/transform errors:"));
        for (const err of errors) {
          console2.log(`In file ${err.filename}:
${err.error}

`);
        }
        console2.log(errors);
      }
      console2.log(
        table.table([
          [chalk.bold.blueBright("Codemod name"), chalk.bold.blueBright("Count")],
          ...Object.entries(stats)
        ])
      );
      if (manualMigrations.length > 0) {
        console2.log(chalk.bold.blueBright("MANUAL MIGRATIONS:\n"));
        for (const migration of manualMigrations) {
          console2.log(
            `${migration.file} ${migration.lineStart}:${migration.columnStart}-${migration.lineEnd}:${migration.columnEnd}
${migration.message}

${migration.snippet}

---

`
          );
        }
      }
    } else {
      bar.update(filesProcessed, { errors: errors.length });
    }
  };
  return defaultCliProgressHandler;
};

// src/cli.ts
function createVueMetamorphCli(options) {
  const program = new Command();
  const defaultCliProgressHandler = createDefaultCliProgressHandler(console);
  program.requiredOption("--files <glob>", "Run transforms against these files", "**/src/**/*").requiredOption("--plugins <glob...>", "Run only these plugins using micromatch queries", "*").option("--list-plugins", "Print a list of plugins.");
  options.additionalCliOptions?.(program);
  let aborted = false;
  const run = async (argv = process.argv) => {
    program.parse(argv);
    const opts = program.opts();
    const stats = {};
    if (opts.listPlugins) {
      process.stdout.write(`${options.plugins.flat().map((plugin) => plugin.name).join("\n")}
`);
      process.exit(0);
    }
    const files = globSync(opts.files, {
      absolute: true,
      nodir: true,
      ignore: {
        ignored(p) {
          if (p.fullpath().includes("node_modules")) {
            return true;
          }
          if (!/\.(vue|ts|js|tsx|jsx|css|scss|less|sass|styl)$/.test(p.fullpath())) {
            return true;
          }
          return false;
        }
      }
    });
    const plugins = options.plugins.flat().filter((plugin) => micromatch.isMatch(plugin.name, opts.plugins));
    const codemodPlugins = plugins.filter((plugin) => plugin.type === "codemod");
    const manualMigrationPlugins = plugins.filter((plugin) => plugin.type === "manual");
    const manualMigrationReports = [];
    const errors = [];
    let filesProcessed = 0;
    for (const file of files) {
      if (aborted) {
        const progressArgs2 = {
          stats,
          aborted: true,
          done: false,
          filesProcessed,
          filesRemaining: files.length - filesProcessed,
          totalFiles: files.length,
          errors,
          manualMigrations: manualMigrationReports
        };
        if (!options.silent) {
          defaultCliProgressHandler(progressArgs2);
        }
        options.onProgress?.(progressArgs2);
        return;
      }
      try {
        const code = (await fs.readFile(file)).toString("utf-8");
        const newCode = transform(code, file, codemodPlugins, opts);
        let writeFile = false;
        for (const [name, count] of newCode.stats) {
          stats[name] ??= 0;
          stats[name] += count;
          if (count > 0) {
            writeFile = true;
          }
        }
        if (writeFile) {
          await fs.writeFile(file, newCode.code);
        }
        manualMigrationReports.push(
          ...findManualMigrations(newCode.code, file, manualMigrationPlugins, opts)
        );
        const progressArgs2 = {
          stats,
          aborted: false,
          done: false,
          filesProcessed,
          filesRemaining: files.length - filesProcessed,
          totalFiles: files.length,
          errors,
          manualMigrations: manualMigrationReports
        };
        if (!options.silent) {
          defaultCliProgressHandler(progressArgs2);
        }
        options.onProgress?.(progressArgs2);
      } catch (e) {
        if (e instanceof Error) {
          errors.push({
            filename: file,
            error: e
          });
        }
      }
      filesProcessed++;
    }
    const progressArgs = {
      stats,
      aborted: false,
      done: true,
      filesProcessed,
      filesRemaining: files.length - filesProcessed,
      totalFiles: files.length,
      errors,
      manualMigrations: manualMigrationReports
    };
    if (!options.silent) {
      defaultCliProgressHandler(progressArgs);
    }
    options.onProgress?.(progressArgs);
  };
  const abort = () => {
    aborted = true;
  };
  return {
    /**
     * Run the CLI
     */
    run,
    /**
     * Stops progress of the runner
     */
    abort,
    /**
     * Commander arguments
     */
    opts: (argv = process.argv) => {
      program.parseOptions(argv);
      return program.opts();
    }
  };
}

// src/main.ts
var builders3 = {
  ...scriptBuilders,
  ...builders_exports
};
export {
  builders3 as builders,
  createVueMetamorphCli,
  findManualMigrations,
  namedTypes3 as namedTypes,
  postcss2 as postcss,
  transform,
  visit4 as traverseScriptAST
};
