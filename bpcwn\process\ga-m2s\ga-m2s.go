package gam2s

import (
	"context"
	"fmt"
	"io/fs"
	"slices"
	"strings"
	"sync"
	"time"

	cfg "github.com/csee-pm/etl/bpcwn/config"
	"github.com/csee-pm/etl/shared/channel"
	ctx "github.com/csee-pm/etl/shared/context"
	etlDb "github.com/csee-pm/etl/shared/db"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/csee-pm/etl/shared/utils/xlutil"
	"github.com/xuri/excelize/v2"
)

type GaM2sProcess struct {
	procFS fs.ReadFileFS
}

func NewGaM2sProcess(procFS fs.ReadFileFS) GaM2sProcess {
	return GaM2sProcess{procFS: procFS}
}

func (g GaM2sProcess) GetReportData(c context.Context) (GaM2sReport, error) {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)
	workDir := ctx.ExtractWorkDir(c)

	workDate := time.Now().AddDate(0, 0, -2)

	var err error
	if conf.Get("etl.mtd_date") != nil {
		mtdDate := conf.GetString("etl.mtd_date")
		workDate, err = time.Parse("20060102", mtdDate)
		if err != nil {
			return GaM2sReport{}, fmt.Errorf("failed to parse mtd date. %s", err)
		}
	}

	conf.Set("work_date", workDate)

	var data []GaM2sData
	if cfg.UseGaM2sFromFile != "" {
		data, err = g.getGaM2sDataFromFile(c, cfg.UseGaM2sFromFile)
	} else {
		data, err = g.getGaM2sData(c, workDate)
	}

	if err != nil {
		return GaM2sReport{}, err
	}

	csvFilePath := fmt.Sprintf("%s/ga_m2s_%s.csv", workDir, time.Now().Format("20060102150405"))
	if cfg.UseGaM2sFromFile == "" {
		if err := utils.WriteToCsv(csvFilePath, data); err != nil {
			logger.Error("failed to write CSV file", "path", csvFilePath, "error", err)
		} else {
			logger.Info("CSV file written", "path", csvFilePath)
		}
	}

	return g.postProcessData(data)
}

func (g GaM2sProcess) getGaM2sData(c context.Context, mtdDate time.Time) ([]GaM2sData, error) {
	logger := ctx.ExtractLogger(c)

	mtdDtStr := mtdDate.Format("2006-01-02")

	var im3Data []GaM2sData
	var threeData []GaM2sData
	var err error

	var wg sync.WaitGroup
	cCancel, cancel := context.WithCancel(c)
	defer cancel()

	wg.Add(1)
	im3Result := channel.RunAsyncContext(cCancel, func() ([]GaM2sData, error) {
		return g.getIm3GaM2sData(cCancel, mtdDtStr)
	})

	go func() {
		defer wg.Done()
		for res := range im3Result {
			res.Map(func(data []GaM2sData) {
				im3Data = data
			}).MapErr(func(er error) {
				err = er
				logger.Error("failed to get IM3 GA M2S data", "error", er)
				cancel()
			})
		}
	}()

	wg.Add(1)
	threeResult := channel.RunAsyncContext(cCancel, func() ([]GaM2sData, error) {
		return g.get3idGaM2sData(cCancel, mtdDtStr)
	})

	go func() {
		defer wg.Done()
		for res := range threeResult {
			res.Map(func(data []GaM2sData) {
				threeData = data
			}).MapErr(func(er error) {
				err = er
				logger.Error("failed to get 3ID GA M2S data", "error", er)
				cancel()
			})
		}
	}()

	wg.Wait()
	if err != nil {
		return nil, err
	}

	return append(im3Data, threeData...), nil
}

func (g GaM2sProcess) getGaM2sDataFromFile(c context.Context, fpath string) ([]GaM2sData, error) {
	return nil, nil
}

func (g GaM2sProcess) getIm3GaM2sData(c context.Context, mtdDate string) ([]GaM2sData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := g.procFS.ReadFile("files/im3_ga_m2s_mtd.sql")
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt": {Name: "mtd_dt", Value: mtdDate},
	}

	logger.Info("Getting IM3 GA M2S data")

	return etlProc.QueryBigQueryData[GaM2sData](c, string(buf), etlProc.WithBqParams(params))
}

func (g GaM2sProcess) get3idGaM2sData(c context.Context, mtdDate string) ([]GaM2sData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := g.procFS.ReadFile("files/3id_ga_m2s_mtd.sql")
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt": {Name: "mtd_dt", Value: mtdDate},
	}

	logger.Info("Getting 3ID GA M2S data")

	return etlProc.QueryBigQueryData[GaM2sData](c, string(buf), etlProc.WithBqParams(params))
}

func (g GaM2sProcess) postProcessData(data []GaM2sData) (GaM2sReport, error) {
	brandMap := make(map[string]*GaM2sReportData)
	iohData := newGaM2sReportData()

	fmMap := make(map[string]struct{})

	for _, d := range data {
		brand := d.Brand
		if _, ok := brandMap[brand]; !ok {
			brandMap[brand] = newGaM2sReportData()
		}
		brandData := brandMap[brand]

		circle := d.Circle.StringVal
		if circle == "JAYA" {
			circle = "JAKARTA RAYA"
		}

		region := d.Region.StringVal
		flag := strings.ToLower(d.KabuFlag.StringVal)
		if flag == "part of 203" {
			flag = "loss kabu"
		}

		if d.Period == "MTD" {
			asofDate, err := time.Parse("20060102", d.AsofDate)
			if err != nil {
				return GaM2sReport{}, err
			}
			brandData.AsofDate = asofDate
			iohData.AsofDate = asofDate
		}

		if _, ok := brandData.RegionalMap[region]; !ok {
			brandData.RegionalMap[region] = &RegionalGaM2sKabuData{
				EntityType: "REGION",
				EntityName: region,
				MTD:        &Kpi{},
				LMTD:       &Kpi{},
				FM:         make(map[string]*Kpi),
			}
			brandData.RegionalKabuMap[region] = make(map[string]*RegionalGaM2sKabuData)
		}
		regionData := brandData.RegionalMap[region]

		if _, ok := iohData.RegionalMap[region]; !ok {
			iohData.RegionalMap[region] = &RegionalGaM2sKabuData{
				EntityType: "REGION",
				EntityName: region,
				MTD:        &Kpi{},
				LMTD:       &Kpi{},
				FM:         make(map[string]*Kpi),
			}
			iohData.RegionalKabuMap[region] = make(map[string]*RegionalGaM2sKabuData)
		}
		iohRegionData := iohData.RegionalMap[region]

		if _, ok := brandData.RegionalKabuMap[region][flag]; !ok {
			brandData.RegionalKabuMap[region][flag] = &RegionalGaM2sKabuData{
				EntityType: "FLAG",
				EntityName: flag,
				MTD:        &Kpi{},
				LMTD:       &Kpi{},
				FM:         make(map[string]*Kpi),
			}
		}
		regionKabuData := brandData.RegionalKabuMap[region][flag]

		if _, ok := iohData.RegionalKabuMap[region][flag]; !ok {
			iohData.RegionalKabuMap[region][flag] = &RegionalGaM2sKabuData{
				EntityType: "FLAG",
				EntityName: flag,
				MTD:        &Kpi{},
				LMTD:       &Kpi{},
				FM:         make(map[string]*Kpi),
			}
		}
		iohRegionKabuData := iohData.RegionalKabuMap[region][flag]

		if _, ok := brandData.CircleMap[circle]; !ok {
			brandData.CircleMap[circle] = &RegionalGaM2sKabuData{
				EntityType: "CIRCLE",
				EntityName: circle,
				MTD:        &Kpi{},
				LMTD:       &Kpi{},
				FM:         make(map[string]*Kpi),
			}
		}
		circleData := brandData.CircleMap[circle]

		if _, ok := iohData.CircleMap[circle]; !ok {
			iohData.CircleMap[circle] = &RegionalGaM2sKabuData{
				EntityType: "CIRCLE",
				EntityName: circle,
				MTD:        &Kpi{},
				LMTD:       &Kpi{},
				FM:         make(map[string]*Kpi),
			}
		}
		iohCircleData := iohData.CircleMap[circle]

		if _, ok := brandData.NationalKabuMap[flag]; !ok {
			brandData.NationalKabuMap[flag] = &RegionalGaM2sKabuData{
				EntityType: "FLAG",
				EntityName: flag,
				MTD:        &Kpi{},
				LMTD:       &Kpi{},
				FM:         make(map[string]*Kpi),
			}
		}
		nationalKabuData := brandData.NationalKabuMap[flag]

		if _, ok := iohData.NationalKabuMap[flag]; !ok {
			iohData.NationalKabuMap[flag] = &RegionalGaM2sKabuData{
				EntityType: "FLAG",
				EntityName: flag,
				MTD:        &Kpi{},
				LMTD:       &Kpi{},
				FM:         make(map[string]*Kpi),
			}
		}
		iohNationalKabuData := iohData.NationalKabuMap[flag]

		nationalData := brandData.NationalData
		iohNationalData := iohData.NationalData

		switch d.Period {
		case "MTD":
			regionData.MTD.GrossAdds += d.GrossAdds
			regionData.MTD.M2s += d.M2s
			regionData.MtdMonth = d.MonthID
			regionData.KabuCount += d.KabuCount

			regionKabuData.MTD.GrossAdds = d.GrossAdds
			regionKabuData.MTD.M2s = d.M2s
			regionKabuData.MtdMonth = d.MonthID
			regionKabuData.KabuCount = d.KabuCount

			iohRegionData.MTD.GrossAdds += d.GrossAdds
			iohRegionData.MTD.M2s += d.M2s
			iohRegionData.MtdMonth = d.MonthID
			iohRegionData.KabuCount += d.KabuCount

			iohRegionKabuData.MTD.GrossAdds = d.GrossAdds
			iohRegionKabuData.MTD.M2s = d.M2s
			iohRegionKabuData.MtdMonth = d.MonthID
			iohRegionKabuData.KabuCount = d.KabuCount

			circleData.MTD.GrossAdds += d.GrossAdds
			circleData.MTD.M2s += d.M2s
			circleData.MtdMonth = d.MonthID
			circleData.KabuCount += d.KabuCount

			iohCircleData.MTD.GrossAdds += d.GrossAdds
			iohCircleData.MTD.M2s += d.M2s
			iohCircleData.MtdMonth = d.MonthID
			iohCircleData.KabuCount += d.KabuCount

			nationalData.MTD.GrossAdds += d.GrossAdds
			nationalData.MTD.M2s += d.M2s
			nationalData.MtdMonth = d.MonthID
			nationalData.KabuCount += d.KabuCount

			iohNationalData.MTD.GrossAdds += d.GrossAdds
			iohNationalData.MTD.M2s += d.M2s
			iohNationalData.MtdMonth = d.MonthID
			iohNationalData.KabuCount += d.KabuCount

			nationalKabuData.MTD.GrossAdds += d.GrossAdds
			nationalKabuData.MTD.M2s += d.M2s
			nationalKabuData.MtdMonth = d.MonthID
			nationalKabuData.KabuCount += d.KabuCount

			iohNationalKabuData.MTD.GrossAdds += d.GrossAdds
			iohNationalKabuData.MTD.M2s += d.M2s
			iohNationalKabuData.MtdMonth = d.MonthID
			iohNationalKabuData.KabuCount += d.KabuCount
		case "LMTD":
			regionData.LMTD.GrossAdds += d.GrossAdds
			regionData.LMTD.M2s += d.M2s
			regionData.LmtdMonth = d.MonthID

			regionKabuData.LMTD.GrossAdds = d.GrossAdds
			regionKabuData.LMTD.M2s = d.M2s
			regionKabuData.LmtdMonth = d.MonthID

			iohRegionData.LMTD.GrossAdds += d.GrossAdds
			iohRegionData.LMTD.M2s += d.M2s
			iohRegionData.LmtdMonth = d.MonthID

			iohRegionKabuData.LMTD.GrossAdds = d.GrossAdds
			iohRegionKabuData.LMTD.M2s = d.M2s
			iohRegionKabuData.LmtdMonth = d.MonthID

			circleData.LMTD.GrossAdds += d.GrossAdds
			circleData.LMTD.M2s += d.M2s
			circleData.LmtdMonth = d.MonthID

			iohCircleData.LMTD.GrossAdds += d.GrossAdds
			iohCircleData.LMTD.M2s += d.M2s
			iohCircleData.LmtdMonth = d.MonthID

			nationalData.LMTD.GrossAdds += d.GrossAdds
			nationalData.LMTD.M2s += d.M2s
			nationalData.LmtdMonth = d.MonthID

			iohNationalData.LMTD.GrossAdds += d.GrossAdds
			iohNationalData.LMTD.M2s += d.M2s
			iohNationalData.LmtdMonth = d.MonthID

			nationalKabuData.LMTD.GrossAdds += d.GrossAdds
			nationalKabuData.LMTD.M2s += d.M2s
			nationalKabuData.LmtdMonth = d.MonthID

			iohNationalKabuData.LMTD.GrossAdds += d.GrossAdds
			iohNationalKabuData.LMTD.M2s += d.M2s
			iohNationalKabuData.LmtdMonth = d.MonthID

		case "FM":
			fmMap[d.MonthID] = struct{}{}
			if _, ok := regionData.FM[d.MonthID]; !ok {
				regionData.FM[d.MonthID] = &Kpi{}
			}
			regionData.FM[d.MonthID].GrossAdds += d.GrossAdds
			regionData.FM[d.MonthID].M2s += d.M2s

			if _, ok := regionKabuData.FM[d.MonthID]; !ok {
				regionKabuData.FM[d.MonthID] = &Kpi{}
			}
			regionKabuData.FM[d.MonthID].GrossAdds = d.GrossAdds
			regionKabuData.FM[d.MonthID].M2s = d.M2s

			if _, ok := iohRegionData.FM[d.MonthID]; !ok {
				iohRegionData.FM[d.MonthID] = &Kpi{}
			}
			iohRegionData.FM[d.MonthID].GrossAdds += d.GrossAdds
			iohRegionData.FM[d.MonthID].M2s += d.M2s

			if _, ok := iohRegionKabuData.FM[d.MonthID]; !ok {
				iohRegionKabuData.FM[d.MonthID] = &Kpi{}
			}
			iohRegionKabuData.FM[d.MonthID].GrossAdds = d.GrossAdds
			iohRegionKabuData.FM[d.MonthID].M2s = d.M2s

			if _, ok := circleData.FM[d.MonthID]; !ok {
				circleData.FM[d.MonthID] = &Kpi{}
			}
			circleData.FM[d.MonthID].GrossAdds += d.GrossAdds
			circleData.FM[d.MonthID].M2s += d.M2s

			if _, ok := iohCircleData.FM[d.MonthID]; !ok {
				iohCircleData.FM[d.MonthID] = &Kpi{}
			}
			iohCircleData.FM[d.MonthID].GrossAdds += d.GrossAdds
			iohCircleData.FM[d.MonthID].M2s += d.M2s

			if _, ok := nationalData.FM[d.MonthID]; !ok {
				nationalData.FM[d.MonthID] = &Kpi{}
			}
			nationalData.FM[d.MonthID].GrossAdds += d.GrossAdds
			nationalData.FM[d.MonthID].M2s += d.M2s

			if _, ok := iohNationalData.FM[d.MonthID]; !ok {
				iohNationalData.FM[d.MonthID] = &Kpi{}
			}
			iohNationalData.FM[d.MonthID].GrossAdds += d.GrossAdds
			iohNationalData.FM[d.MonthID].M2s += d.M2s

			if _, ok := nationalKabuData.FM[d.MonthID]; !ok {
				nationalKabuData.FM[d.MonthID] = &Kpi{}
			}
			nationalKabuData.FM[d.MonthID].GrossAdds += d.GrossAdds
			nationalKabuData.FM[d.MonthID].M2s += d.M2s

			if _, ok := iohNationalKabuData.FM[d.MonthID]; !ok {
				iohNationalKabuData.FM[d.MonthID] = &Kpi{}
			}
			iohNationalKabuData.FM[d.MonthID].GrossAdds += d.GrossAdds
			iohNationalKabuData.FM[d.MonthID].M2s += d.M2s
		}
	}

	fmList := utils.MapToList(fmMap, func(key string, value struct{}) string {
		return key
	})

	slices.SortFunc(fmList, func(a, b string) int {
		return strings.Compare(b, a)
	})

	if len(fmList) > 4 {
		fmList = fmList[:4]
	}
	iohData.FmList = fmList
	brandMap["IM3"].FmList = fmList
	brandMap["3ID"].FmList = fmList

	return GaM2sReport{
		IOH:   iohData,
		IM3:   brandMap["IM3"],
		Three: brandMap["3ID"],
	}, nil
}

func newGaM2sReportData() *GaM2sReportData {
	return &GaM2sReportData{
		CircleMap:       make(map[string]*RegionalGaM2sKabuData),
		RegionalMap:     make(map[string]*RegionalGaM2sKabuData),
		RegionalKabuMap: make(map[string]map[string]*RegionalGaM2sKabuData),
		NationalData: &RegionalGaM2sKabuData{
			EntityType: "NATIONAL",
			EntityName: "INDONESIA",
			MTD:        &Kpi{},
			LMTD:       &Kpi{},
			FM:         make(map[string]*Kpi),
		},
		NationalKabuMap: make(map[string]*RegionalGaM2sKabuData),
	}
}

var (
	GaM2sSheet        = "Summary"
	circle_col        = 2
	region_col        = 3
	flag_col          = 4
	total_kabu_col    = []int{5, 27}
	ioh_ga_start_col  = 7
	ioh_m2s_start_col = 16
	tri_ga_start_col  = 29
	im3_ga_start_col  = 38
)

func (g GaM2sProcess) WriteReport(xl *excelize.File, data GaM2sReport) error {
	shName := GaM2sSheet
	dtStr := data.IOH.AsofDate.Format("02 Jan")
	if err := xl.SetCellValue(shName, "B4", fmt.Sprintf("IOH data as of %s", dtStr)); err != nil {
		return fmt.Errorf("failed to write asof date. %s", err)
	}
	if err := xl.SetCellValue(shName, "X4", fmt.Sprintf("GAD's data as of %s", dtStr)); err != nil {
		return fmt.Errorf("failed to write asof date. %s", err)
	}

	fmList := data.IOH.FmList

	for i, mth := range fmList {
		if i > 3 {
			break
		}

		mthDate, err := time.Parse("200601", mth)
		if err != nil {
			return err
		}

		mthName := mthDate.Format("Jan-06")
		if err := xl.SetCellValue(shName, xlutil.Cell(9, ioh_ga_start_col+(3-i)).Address(), mthName); err != nil {
			return fmt.Errorf("failed to write month name for ioh_ga. %s", err)
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(9, ioh_m2s_start_col+(3-i)).Address(), mthName); err != nil {
			return fmt.Errorf("failed to write month name for ioh_m2s. %s", err)
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(9, tri_ga_start_col+(3-i)).Address(), mthName); err != nil {
			return fmt.Errorf("failed to write month name for 3id_ga. %s", err)
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(9, im3_ga_start_col+(3-i)).Address(), mthName); err != nil {
			return fmt.Errorf("failed to write month name for im3_ga. %s", err)
		}
	}

	for r := 6; r <= 43; r++ {
		circle, err := xl.GetCellValue(shName, xlutil.Cell(r, circle_col).Address())
		if err != nil {
			return err
		}
		circle = strings.TrimSpace(strings.ReplaceAll(circle, " Total", ""))
		region, err := xl.GetCellValue(shName, xlutil.Cell(r, region_col).Address())
		if err != nil {
			return err
		}
		region = strings.TrimSpace(region)
		flag, err := xl.GetCellValue(shName, xlutil.Cell(r, flag_col).Address())
		if err != nil {
			return err
		}
		flag = strings.ToLower(strings.TrimSpace(flag))

		var iohData *RegionalGaM2sKabuData
		var im3Data *RegionalGaM2sKabuData
		var threeData *RegionalGaM2sKabuData

		if region == "" && flag == "" {
			if circle == "TOTAL NATIONAL" {
				iohData = data.IOH.NationalData
				im3Data = data.IM3.NationalData
				threeData = data.Three.NationalData
			} else {
				iohData = data.IOH.CircleMap[circle]
				im3Data = data.IM3.CircleMap[circle]
				threeData = data.Three.CircleMap[circle]
			}
		}

		if flag != "" {
			if region == "" {
				iohData = data.IOH.NationalKabuMap[flag]
				im3Data = data.IM3.NationalKabuMap[flag]
				threeData = data.Three.NationalKabuMap[flag]
			} else {
				iohData = data.IOH.RegionalKabuMap[region][flag]
				im3Data = data.IM3.RegionalKabuMap[region][flag]
				threeData = data.Three.RegionalKabuMap[region][flag]
			}
		}

		if iohData != nil {
			if err := g.writeMtdData(xl, iohData, r, ioh_ga_start_col, ioh_m2s_start_col); err != nil {
				return fmt.Errorf("failed to write mtd data for IOH circle: %s region: %s flag: %s. %s", circle, region, flag, err)
			}

			if err := g.writeFmData(xl, iohData.FM, fmList, r, ioh_ga_start_col, ioh_m2s_start_col); err != nil {
				return fmt.Errorf("failed to write fm data for IOH circle: %s region: %s flag: %s. %s", circle, region, flag, err)
			}
		}

		totalKabu := 0

		if im3Data != nil {
			if err := g.writeMtdData(xl, im3Data, r, im3_ga_start_col, 0); err != nil {
				return fmt.Errorf("failed to write mtd data for IM3 circle: %s region: %s flag: %s. %s", circle, region, flag, err)
			}

			if err := g.writeFmData(xl, im3Data.FM, fmList, r, im3_ga_start_col, 0); err != nil {
				return fmt.Errorf("failed to write fm data for IM3 circle: %s region: %s flag: %s. %s", circle, region, flag, err)
			}

			totalKabu = im3Data.KabuCount
		}

		if threeData != nil {
			if err := g.writeMtdData(xl, threeData, r, tri_ga_start_col, 0); err != nil {
				return fmt.Errorf("failed to write mtd data for 3ID circle: %s region: %s flag: %s. %s", circle, region, flag, err)
			}

			if err := g.writeFmData(xl, threeData.FM, fmList, r, tri_ga_start_col, 0); err != nil {
				return fmt.Errorf("failed to write fm data for 3ID circle: %s region: %s flag: %s. %s", circle, region, flag, err)
			}

			if totalKabu == 0 {
				totalKabu = threeData.KabuCount
			}
		}

		for _, col := range total_kabu_col {
			if err := xl.SetCellValue(shName, xlutil.Cell(r, col).Address(), totalKabu); err != nil {
				return fmt.Errorf("failed to write total kabu. %s", err)
			}
		}
	}

	return nil
}

func (g GaM2sProcess) writeMtdData(xl *excelize.File, regionData *RegionalGaM2sKabuData, row int, gaStartCol int, m2sStartCol int) error {
	shName := GaM2sSheet
	gaMtdCol := gaStartCol + 5
	m2sMtdCol := m2sStartCol + 5
	gaLmtdCol := gaStartCol + 4
	m2sLmtdCol := m2sStartCol + 4
	gaAbsCol := gaStartCol + 6
	gaPctCol := gaStartCol + 7
	m2sPctCol := m2sStartCol + 6

	if err := xl.SetCellValue(shName, xlutil.Cell(row, gaMtdCol).Address(), regionData.MTD.GrossAdds/1000); err != nil {
		return fmt.Errorf("failed to write mtd ga. %s", err)
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(row, gaLmtdCol).Address(), regionData.LMTD.GrossAdds/1000); err != nil {
		return fmt.Errorf("failed to write lmtd ga. %s", err)
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(row, gaAbsCol).Address(), (regionData.MTD.GrossAdds-regionData.LMTD.GrossAdds)/1000); err != nil {
		return fmt.Errorf("failed to write ga abs. %s", err)
	}

	if err := xl.SetCellValue(shName, xlutil.Cell(row, gaPctCol).Address(), (float64(regionData.MTD.GrossAdds)-float64(regionData.LMTD.GrossAdds))/float64(regionData.LMTD.GrossAdds)); err != nil {
		return fmt.Errorf("failed to write ga pct. %s", err)
	}

	if m2sStartCol > 0 {
		mtdMonth, err := time.Parse("200601", regionData.MtdMonth)
		if err != nil {
			fmt.Printf("failed to parse mtd month for entity %s name %s. %s", regionData.EntityType, regionData.EntityName, err)
			return nil
		}
		lmtdMonth, err := time.Parse("200601", regionData.LmtdMonth)
		if err != nil {
			fmt.Printf("failed to parse lmtd month for entity %s name %s. %s", regionData.EntityType, regionData.EntityName, err)
			return nil
		}

		mtdM2Month := mtdMonth.AddDate(0, -2, 0).Format("200601")
		lmtdM2Month := lmtdMonth.AddDate(0, -2, 0).Format("200601")
		mtdM2Ga := regionData.FM[mtdM2Month].GrossAdds
		lmtdM2Ga := regionData.FM[lmtdM2Month].GrossAdds

		mtdM2sPct := 0.0
		if mtdM2Ga > 0 {
			mtdM2sPct = float64(regionData.MTD.M2s) / float64(mtdM2Ga)
		}
		lmtdM2sPct := 0.0
		if lmtdM2Ga > 0 {
			lmtdM2sPct = float64(regionData.LMTD.M2s) / float64(lmtdM2Ga)
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(row, m2sMtdCol).Address(), mtdM2sPct); err != nil {
			return fmt.Errorf("failed to write mtd m2s pct. %s", err)
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(row, m2sLmtdCol).Address(), lmtdM2sPct); err != nil {
			return fmt.Errorf("failed to write lmtd m2s pct. %s", err)
		}

		if err := xl.SetCellValue(shName, xlutil.Cell(row, m2sPctCol).Address(), (float64(mtdM2sPct)/float64(lmtdM2sPct) - 1)); err != nil {
			return fmt.Errorf("failed to write m2s pct. %s", err)
		}
	}

	return nil
}

func (g GaM2sProcess) writeFmData(xl *excelize.File, fmData map[string]*Kpi, fmList []string, row int, gaStartCol int, m2sStartCol int) error {
	for i, mth := range fmList {
		if i > 3 {
			break
		}

		mthData, ok := fmData[mth]
		if !ok {
			continue
		}

		gaCol := gaStartCol + (3 - i)
		m2sCol := m2sStartCol + (3 - i)

		mthDate, err := time.Parse("200601", mth)
		if err != nil {
			return err
		}

		if err := xl.SetCellValue(GaM2sSheet, xlutil.Cell(row, gaCol).Address(), mthData.GrossAdds/1000); err != nil {
			return fmt.Errorf("failed to write fm ga. %s", err)
		}

		if m2sStartCol < 1 {
			continue
		}

		m2smonth := mthDate.AddDate(0, -2, 0).Format("200601")

		m2sData, ok := fmData[m2smonth]
		if !ok {
			continue
		}

		m2sPct := 0.0
		if m2sData.GrossAdds > 0 {
			m2sPct = float64(mthData.M2s) / float64(m2sData.GrossAdds)
		}

		if err := xl.SetCellValue(GaM2sSheet, xlutil.Cell(row, m2sCol).Address(), m2sPct); err != nil {
			return fmt.Errorf("failed to write fm m2s pct on row %d col %d. %s", row, m2sCol, err)
		}
	}

	return nil
}
