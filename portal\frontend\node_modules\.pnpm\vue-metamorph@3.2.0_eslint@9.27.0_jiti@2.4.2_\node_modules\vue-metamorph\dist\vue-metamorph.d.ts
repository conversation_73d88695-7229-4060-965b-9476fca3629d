import { AnyTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { ArrayExpressionBuilder } from 'ast-types/gen/builders';
import { ArrayPatternBuilder } from 'ast-types/gen/builders';
import { ArrayTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { ArrowFunctionExpressionBuilder } from 'ast-types/gen/builders';
import { AssignmentExpressionBuilder } from 'ast-types/gen/builders';
import { AssignmentPatternBuilder } from 'ast-types/gen/builders';
import { ASTNode } from 'ast-types';
import { AwaitExpressionBuilder } from 'ast-types/gen/builders';
import { BigIntLiteralBuilder } from 'ast-types/gen/builders';
import { BigIntLiteralTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { BigIntTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { BinaryExpressionBuilder } from 'ast-types/gen/builders';
import { BindExpressionBuilder } from 'ast-types/gen/builders';
import { BlockBuilder } from 'ast-types/gen/builders';
import { BlockStatementBuilder } from 'ast-types/gen/builders';
import { BooleanLiteralBuilder } from 'ast-types/gen/builders';
import { BooleanLiteralTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { BooleanTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { BreakStatementBuilder } from 'ast-types/gen/builders';
import { CallExpressionBuilder } from 'ast-types/gen/builders';
import { CatchClauseBuilder } from 'ast-types/gen/builders';
import { ChainExpressionBuilder } from 'ast-types/gen/builders';
import { ClassBodyBuilder } from 'ast-types/gen/builders';
import { ClassDeclarationBuilder } from 'ast-types/gen/builders';
import { ClassExpressionBuilder } from 'ast-types/gen/builders';
import { ClassImplementsBuilder } from 'ast-types/gen/builders';
import { ClassMethodBuilder } from 'ast-types/gen/builders';
import { ClassPrivateMethodBuilder } from 'ast-types/gen/builders';
import { ClassPrivatePropertyBuilder } from 'ast-types/gen/builders';
import { ClassPropertyBuilder } from 'ast-types/gen/builders';
import { ClassPropertyDefinitionBuilder } from 'ast-types/gen/builders';
import { Command } from 'commander';
import { CommentBlockBuilder } from 'ast-types/gen/builders';
import { CommentLineBuilder } from 'ast-types/gen/builders';
import { ComprehensionBlockBuilder } from 'ast-types/gen/builders';
import { ComprehensionExpressionBuilder } from 'ast-types/gen/builders';
import { ConditionalExpressionBuilder } from 'ast-types/gen/builders';
import { ContinueStatementBuilder } from 'ast-types/gen/builders';
import { DebuggerStatementBuilder } from 'ast-types/gen/builders';
import { DeclareClassBuilder } from 'ast-types/gen/builders';
import { DeclaredPredicateBuilder } from 'ast-types/gen/builders';
import { DeclareExportAllDeclarationBuilder } from 'ast-types/gen/builders';
import { DeclareExportDeclarationBuilder } from 'ast-types/gen/builders';
import { DeclareFunctionBuilder } from 'ast-types/gen/builders';
import { DeclareInterfaceBuilder } from 'ast-types/gen/builders';
import { DeclareModuleBuilder } from 'ast-types/gen/builders';
import { DeclareModuleExportsBuilder } from 'ast-types/gen/builders';
import { DeclareOpaqueTypeBuilder } from 'ast-types/gen/builders';
import { DeclareTypeAliasBuilder } from 'ast-types/gen/builders';
import { DeclareVariableBuilder } from 'ast-types/gen/builders';
import { DecoratorBuilder } from 'ast-types/gen/builders';
import { DirectiveBuilder } from 'ast-types/gen/builders';
import { DirectiveLiteralBuilder } from 'ast-types/gen/builders';
import { DoExpressionBuilder } from 'ast-types/gen/builders';
import { DoWhileStatementBuilder } from 'ast-types/gen/builders';
import { EmptyStatementBuilder } from 'ast-types/gen/builders';
import { EmptyTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { EnumBooleanBodyBuilder } from 'ast-types/gen/builders';
import { EnumBooleanMemberBuilder } from 'ast-types/gen/builders';
import { EnumDeclarationBuilder } from 'ast-types/gen/builders';
import { EnumDefaultedMemberBuilder } from 'ast-types/gen/builders';
import { EnumNumberBodyBuilder } from 'ast-types/gen/builders';
import { EnumNumberMemberBuilder } from 'ast-types/gen/builders';
import { EnumStringBodyBuilder } from 'ast-types/gen/builders';
import { EnumStringMemberBuilder } from 'ast-types/gen/builders';
import { EnumSymbolBodyBuilder } from 'ast-types/gen/builders';
import { ExistentialTypeParamBuilder } from 'ast-types/gen/builders';
import { ExistsTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { ExportAllDeclarationBuilder } from 'ast-types/gen/builders';
import { ExportBatchSpecifierBuilder } from 'ast-types/gen/builders';
import { ExportDeclarationBuilder } from 'ast-types/gen/builders';
import { ExportDefaultDeclarationBuilder } from 'ast-types/gen/builders';
import { ExportDefaultSpecifierBuilder } from 'ast-types/gen/builders';
import { ExportNamedDeclarationBuilder } from 'ast-types/gen/builders';
import { ExportNamespaceSpecifierBuilder } from 'ast-types/gen/builders';
import { ExportSpecifierBuilder } from 'ast-types/gen/builders';
import { ExpressionKind } from 'ast-types/gen/kinds';
import { ExpressionStatementBuilder } from 'ast-types/gen/builders';
import { FileBuilder } from 'ast-types/gen/builders';
import { ForAwaitStatementBuilder } from 'ast-types/gen/builders';
import { ForInStatementBuilder } from 'ast-types/gen/builders';
import { ForOfStatementBuilder } from 'ast-types/gen/builders';
import { ForStatementBuilder } from 'ast-types/gen/builders';
import { FunctionDeclarationBuilder } from 'ast-types/gen/builders';
import { FunctionExpressionBuilder } from 'ast-types/gen/builders';
import { FunctionTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { FunctionTypeParamBuilder } from 'ast-types/gen/builders';
import { GeneratorExpressionBuilder } from 'ast-types/gen/builders';
import { GenericTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { IdentifierBuilder } from 'ast-types/gen/builders';
import { IfStatementBuilder } from 'ast-types/gen/builders';
import { ImportBuilder } from 'ast-types/gen/builders';
import { ImportDeclarationBuilder } from 'ast-types/gen/builders';
import { ImportDefaultSpecifierBuilder } from 'ast-types/gen/builders';
import { ImportExpressionBuilder } from 'ast-types/gen/builders';
import { ImportNamespaceSpecifierBuilder } from 'ast-types/gen/builders';
import { ImportSpecifierBuilder } from 'ast-types/gen/builders';
import { InferredPredicateBuilder } from 'ast-types/gen/builders';
import { InterfaceDeclarationBuilder } from 'ast-types/gen/builders';
import { InterfaceExtendsBuilder } from 'ast-types/gen/builders';
import { InterfaceTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { InterpreterDirectiveBuilder } from 'ast-types/gen/builders';
import { IntersectionTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { JSXAttributeBuilder } from 'ast-types/gen/builders';
import { JSXClosingElementBuilder } from 'ast-types/gen/builders';
import { JSXClosingFragmentBuilder } from 'ast-types/gen/builders';
import { JSXElementBuilder } from 'ast-types/gen/builders';
import { JSXEmptyExpressionBuilder } from 'ast-types/gen/builders';
import { JSXExpressionContainerBuilder } from 'ast-types/gen/builders';
import { JSXFragmentBuilder } from 'ast-types/gen/builders';
import { JSXIdentifierBuilder } from 'ast-types/gen/builders';
import { JSXMemberExpressionBuilder } from 'ast-types/gen/builders';
import { JSXNamespacedNameBuilder } from 'ast-types/gen/builders';
import { JSXOpeningElementBuilder } from 'ast-types/gen/builders';
import { JSXOpeningFragmentBuilder } from 'ast-types/gen/builders';
import { JSXSpreadAttributeBuilder } from 'ast-types/gen/builders';
import { JSXSpreadChildBuilder } from 'ast-types/gen/builders';
import { JSXTextBuilder } from 'ast-types/gen/builders';
import * as Kinds from 'ast-types/gen/kinds';
import { LabeledStatementBuilder } from 'ast-types/gen/builders';
import { LineBuilder } from 'ast-types/gen/builders';
import { LiteralBuilder } from 'ast-types/gen/builders';
import { LogicalExpressionBuilder } from 'ast-types/gen/builders';
import { MemberExpressionBuilder } from 'ast-types/gen/builders';
import { MemberTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { MetaPropertyBuilder } from 'ast-types/gen/builders';
import { MethodDefinitionBuilder } from 'ast-types/gen/builders';
import { MixedTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { namedTypes } from 'ast-types';
import { NewExpressionBuilder } from 'ast-types/gen/builders';
import { NoopBuilder } from 'ast-types/gen/builders';
import { NullableTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { NullLiteralBuilder } from 'ast-types/gen/builders';
import { NullLiteralTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { NullTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { NumberLiteralTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { NumberTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { NumericLiteralBuilder } from 'ast-types/gen/builders';
import { NumericLiteralTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { ObjectExpressionBuilder } from 'ast-types/gen/builders';
import { ObjectMethodBuilder } from 'ast-types/gen/builders';
import { ObjectPatternBuilder } from 'ast-types/gen/builders';
import { ObjectPropertyBuilder } from 'ast-types/gen/builders';
import { ObjectTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { ObjectTypeCallPropertyBuilder } from 'ast-types/gen/builders';
import { ObjectTypeIndexerBuilder } from 'ast-types/gen/builders';
import { ObjectTypeInternalSlotBuilder } from 'ast-types/gen/builders';
import { ObjectTypePropertyBuilder } from 'ast-types/gen/builders';
import { ObjectTypeSpreadPropertyBuilder } from 'ast-types/gen/builders';
import { OpaqueTypeBuilder } from 'ast-types/gen/builders';
import { OptionalCallExpressionBuilder } from 'ast-types/gen/builders';
import { OptionalMemberExpressionBuilder } from 'ast-types/gen/builders';
import { OptionValues } from 'commander';
import { ParenthesizedExpressionBuilder } from 'ast-types/gen/builders';
import { PatternKind } from 'ast-types/gen/kinds';
import postcss from 'postcss';
import { PrivateNameBuilder } from 'ast-types/gen/builders';
import { ProgramBuilder } from 'ast-types/gen/builders';
import { PropertyBuilder } from 'ast-types/gen/builders';
import { PropertyPatternBuilder } from 'ast-types/gen/builders';
import { QualifiedTypeIdentifierBuilder } from 'ast-types/gen/builders';
import { RegExpLiteralBuilder } from 'ast-types/gen/builders';
import { RestElementBuilder } from 'ast-types/gen/builders';
import { RestPropertyBuilder } from 'ast-types/gen/builders';
import { ReturnStatementBuilder } from 'ast-types/gen/builders';
import { SequenceExpressionBuilder } from 'ast-types/gen/builders';
import { SpreadElementBuilder } from 'ast-types/gen/builders';
import { SpreadElementPatternBuilder } from 'ast-types/gen/builders';
import { SpreadPropertyBuilder } from 'ast-types/gen/builders';
import { SpreadPropertyPatternBuilder } from 'ast-types/gen/builders';
import { StatementKind } from 'ast-types/gen/kinds';
import { StringLiteralBuilder } from 'ast-types/gen/builders';
import { StringLiteralTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { StringTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { SuperBuilder } from 'ast-types/gen/builders';
import { SwitchCaseBuilder } from 'ast-types/gen/builders';
import { SwitchStatementBuilder } from 'ast-types/gen/builders';
import { SymbolTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { TaggedTemplateExpressionBuilder } from 'ast-types/gen/builders';
import { TemplateElementBuilder } from 'ast-types/gen/builders';
import { TemplateLiteralBuilder } from 'ast-types/gen/builders';
import { ThisExpressionBuilder } from 'ast-types/gen/builders';
import { ThisTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { ThrowStatementBuilder } from 'ast-types/gen/builders';
import { visit as traverseScriptAST } from 'ast-types';
import { TryStatementBuilder } from 'ast-types/gen/builders';
import { TSAnyKeywordBuilder } from 'ast-types/gen/builders';
import { TSArrayTypeBuilder } from 'ast-types/gen/builders';
import { TSAsExpressionBuilder } from 'ast-types/gen/builders';
import { TSBigIntKeywordBuilder } from 'ast-types/gen/builders';
import { TSBooleanKeywordBuilder } from 'ast-types/gen/builders';
import { TSCallSignatureDeclarationBuilder } from 'ast-types/gen/builders';
import { TSConditionalTypeBuilder } from 'ast-types/gen/builders';
import { TSConstructorTypeBuilder } from 'ast-types/gen/builders';
import { TSConstructSignatureDeclarationBuilder } from 'ast-types/gen/builders';
import { TSDeclareFunctionBuilder } from 'ast-types/gen/builders';
import { TSDeclareMethodBuilder } from 'ast-types/gen/builders';
import { TSEnumDeclarationBuilder } from 'ast-types/gen/builders';
import { TSEnumMemberBuilder } from 'ast-types/gen/builders';
import { TSExportAssignmentBuilder } from 'ast-types/gen/builders';
import { TSExpressionWithTypeArgumentsBuilder } from 'ast-types/gen/builders';
import { TSExternalModuleReferenceBuilder } from 'ast-types/gen/builders';
import { TSFunctionTypeBuilder } from 'ast-types/gen/builders';
import { TSImportEqualsDeclarationBuilder } from 'ast-types/gen/builders';
import { TSImportTypeBuilder } from 'ast-types/gen/builders';
import { TSIndexedAccessTypeBuilder } from 'ast-types/gen/builders';
import { TSIndexSignatureBuilder } from 'ast-types/gen/builders';
import { TSInferTypeBuilder } from 'ast-types/gen/builders';
import { TSInterfaceBodyBuilder } from 'ast-types/gen/builders';
import { TSInterfaceDeclarationBuilder } from 'ast-types/gen/builders';
import { TSIntersectionTypeBuilder } from 'ast-types/gen/builders';
import { TSLiteralTypeBuilder } from 'ast-types/gen/builders';
import { TSMappedTypeBuilder } from 'ast-types/gen/builders';
import { TSMethodSignatureBuilder } from 'ast-types/gen/builders';
import { TSModuleBlockBuilder } from 'ast-types/gen/builders';
import { TSModuleDeclarationBuilder } from 'ast-types/gen/builders';
import { TSNamedTupleMemberBuilder } from 'ast-types/gen/builders';
import { TSNamespaceExportDeclarationBuilder } from 'ast-types/gen/builders';
import { TSNeverKeywordBuilder } from 'ast-types/gen/builders';
import { TSNonNullExpressionBuilder } from 'ast-types/gen/builders';
import { TSNullKeywordBuilder } from 'ast-types/gen/builders';
import { TSNumberKeywordBuilder } from 'ast-types/gen/builders';
import { TSObjectKeywordBuilder } from 'ast-types/gen/builders';
import { TSOptionalTypeBuilder } from 'ast-types/gen/builders';
import { TSParameterPropertyBuilder } from 'ast-types/gen/builders';
import { TSParenthesizedTypeBuilder } from 'ast-types/gen/builders';
import { TSPropertySignatureBuilder } from 'ast-types/gen/builders';
import { TSQualifiedNameBuilder } from 'ast-types/gen/builders';
import { TSRestTypeBuilder } from 'ast-types/gen/builders';
import { TSStringKeywordBuilder } from 'ast-types/gen/builders';
import { TSSymbolKeywordBuilder } from 'ast-types/gen/builders';
import { TSThisTypeBuilder } from 'ast-types/gen/builders';
import { TSTupleTypeBuilder } from 'ast-types/gen/builders';
import { TSTypeAliasDeclarationBuilder } from 'ast-types/gen/builders';
import { TSTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { TSTypeAssertionBuilder } from 'ast-types/gen/builders';
import { TSTypeLiteralBuilder } from 'ast-types/gen/builders';
import { TSTypeOperatorBuilder } from 'ast-types/gen/builders';
import { TSTypeParameterBuilder } from 'ast-types/gen/builders';
import { TSTypeParameterDeclarationBuilder } from 'ast-types/gen/builders';
import { TSTypeParameterInstantiationBuilder } from 'ast-types/gen/builders';
import { TSTypePredicateBuilder } from 'ast-types/gen/builders';
import { TSTypeQueryBuilder } from 'ast-types/gen/builders';
import { TSTypeReferenceBuilder } from 'ast-types/gen/builders';
import { TSUndefinedKeywordBuilder } from 'ast-types/gen/builders';
import { TSUnionTypeBuilder } from 'ast-types/gen/builders';
import { TSUnknownKeywordBuilder } from 'ast-types/gen/builders';
import { TSVoidKeywordBuilder } from 'ast-types/gen/builders';
import { TupleTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { TypeAliasBuilder } from 'ast-types/gen/builders';
import { TypeAnnotationBuilder } from 'ast-types/gen/builders';
import { TypeCastExpressionBuilder } from 'ast-types/gen/builders';
import { TypeofTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { TypeParameterBuilder } from 'ast-types/gen/builders';
import { TypeParameterDeclarationBuilder } from 'ast-types/gen/builders';
import { TypeParameterInstantiationBuilder } from 'ast-types/gen/builders';
import { UnaryExpressionBuilder } from 'ast-types/gen/builders';
import { UnionTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { UpdateExpressionBuilder } from 'ast-types/gen/builders';
import { VariableDeclarationBuilder } from 'ast-types/gen/builders';
import { VariableDeclaratorBuilder } from 'ast-types/gen/builders';
import { VarianceBuilder } from 'ast-types/gen/builders';
import { Visitor } from 'ast-types';
import { VoidTypeAnnotationBuilder } from 'ast-types/gen/builders';
import { WhileStatementBuilder } from 'ast-types/gen/builders';
import { WithStatementBuilder } from 'ast-types/gen/builders';
import { YieldExpressionBuilder } from 'ast-types/gen/builders';

declare namespace AST {
    export {
        HasParent,
        NS,
        Namespace,
        Node,
        VForExpression,
        VOnExpression,
        VSlotScopeExpression,
        VFilterSequenceExpression,
        VFilter,
        VGenericExpression,
        VNode,
        VText,
        VExpressionContainer,
        VIdentifier,
        VDirectiveKey,
        VLiteral,
        VAttribute,
        VDirective,
        VStartTag,
        VEndTag,
        VElement,
        VDocumentFragment,
        VStyleElement,
        traverseNodes
    }
}
export { AST }

declare namespace astHelpers {
    export {
        findFirst,
        findAll,
        findImportDeclaration,
        createNamedImport,
        createDefaultImport,
        createNamespaceImport,
        findVueComponentOptions
    }
}

/**
 * AST Node builders
 * @public
 */
export declare const builders: {
    setParents(node: AST.Node): void;
    vAttribute(key: AST.VAttribute["key"], value: AST.VAttribute["value"]): AST.VAttribute;
    vDirective(key: AST.VDirective["key"], value: AST.VDirective["value"]): AST.VDirective;
    vDirectiveKey(name: AST.VDirectiveKey["name"], argument?: AST.VDirectiveKey["argument"], modifiers?: AST.VDirectiveKey["modifiers"]): AST.VDirectiveKey;
    vDocumentFragment(children: AST.VDocumentFragment["children"]): AST.VDocumentFragment;
    vEndTag(): AST.VEndTag;
    vElement(name: string, startTag: AST.VStartTag, children: AST.VElement["children"], namespace?: AST.VElement["namespace"]): AST.VElement;
    vExpressionContainer(expression: AST.VExpressionContainer["expression"]): AST.VExpressionContainer;
    vForExpression(left: AST.VForExpression["left"], right: AST.VForExpression["right"]): AST.VForExpression;
    vIdentifier(name: AST.VIdentifier["name"], rawName?: AST.VIdentifier["rawName"]): AST.VIdentifier;
    vLiteral(value: AST.VLiteral["value"]): AST.VLiteral;
    vStartTag(attributes: AST.VStartTag["attributes"], selfClosing: AST.VStartTag["selfClosing"]): AST.VStartTag;
    vText(value: AST.VText["value"]): AST.VText;
    vOnExpression(body: AST.VOnExpression["body"]): AST.VOnExpression;
    vFilterSequenceExpression(expression: AST.VFilterSequenceExpression["expression"], filters: AST.VFilterSequenceExpression["filters"]): AST.VFilterSequenceExpression;
    vFilter(callee: AST.VFilter["callee"], args: AST.VFilter["arguments"]): AST.VFilter;
    file: FileBuilder;
    program: ProgramBuilder;
    identifier: IdentifierBuilder;
    blockStatement: BlockStatementBuilder;
    emptyStatement: EmptyStatementBuilder;
    expressionStatement: ExpressionStatementBuilder;
    ifStatement: IfStatementBuilder;
    labeledStatement: LabeledStatementBuilder;
    breakStatement: BreakStatementBuilder;
    continueStatement: ContinueStatementBuilder;
    withStatement: WithStatementBuilder;
    switchStatement: SwitchStatementBuilder;
    switchCase: SwitchCaseBuilder;
    returnStatement: ReturnStatementBuilder;
    throwStatement: ThrowStatementBuilder;
    tryStatement: TryStatementBuilder;
    catchClause: CatchClauseBuilder;
    whileStatement: WhileStatementBuilder;
    doWhileStatement: DoWhileStatementBuilder;
    forStatement: ForStatementBuilder;
    variableDeclaration: VariableDeclarationBuilder;
    forInStatement: ForInStatementBuilder;
    debuggerStatement: DebuggerStatementBuilder;
    functionDeclaration: FunctionDeclarationBuilder;
    functionExpression: FunctionExpressionBuilder;
    variableDeclarator: VariableDeclaratorBuilder;
    thisExpression: ThisExpressionBuilder;
    arrayExpression: ArrayExpressionBuilder;
    objectExpression: ObjectExpressionBuilder;
    property: PropertyBuilder;
    literal: LiteralBuilder;
    sequenceExpression: SequenceExpressionBuilder;
    unaryExpression: UnaryExpressionBuilder;
    binaryExpression: BinaryExpressionBuilder;
    assignmentExpression: AssignmentExpressionBuilder;
    memberExpression: MemberExpressionBuilder;
    updateExpression: UpdateExpressionBuilder;
    logicalExpression: LogicalExpressionBuilder;
    conditionalExpression: ConditionalExpressionBuilder;
    newExpression: NewExpressionBuilder;
    callExpression: CallExpressionBuilder;
    restElement: RestElementBuilder;
    typeAnnotation: TypeAnnotationBuilder;
    tsTypeAnnotation: TSTypeAnnotationBuilder;
    spreadElementPattern: SpreadElementPatternBuilder;
    arrowFunctionExpression: ArrowFunctionExpressionBuilder;
    forOfStatement: ForOfStatementBuilder;
    yieldExpression: YieldExpressionBuilder;
    generatorExpression: GeneratorExpressionBuilder;
    comprehensionBlock: ComprehensionBlockBuilder;
    comprehensionExpression: ComprehensionExpressionBuilder;
    objectProperty: ObjectPropertyBuilder;
    propertyPattern: PropertyPatternBuilder;
    objectPattern: ObjectPatternBuilder;
    arrayPattern: ArrayPatternBuilder;
    spreadElement: SpreadElementBuilder;
    assignmentPattern: AssignmentPatternBuilder;
    methodDefinition: MethodDefinitionBuilder;
    classPropertyDefinition: ClassPropertyDefinitionBuilder;
    classProperty: ClassPropertyBuilder;
    classBody: ClassBodyBuilder;
    classDeclaration: ClassDeclarationBuilder;
    classExpression: ClassExpressionBuilder;
    super: SuperBuilder;
    importSpecifier: ImportSpecifierBuilder;
    importDefaultSpecifier: ImportDefaultSpecifierBuilder;
    importNamespaceSpecifier: ImportNamespaceSpecifierBuilder;
    importDeclaration: ImportDeclarationBuilder;
    exportNamedDeclaration: ExportNamedDeclarationBuilder;
    exportSpecifier: ExportSpecifierBuilder;
    exportDefaultDeclaration: ExportDefaultDeclarationBuilder;
    exportAllDeclaration: ExportAllDeclarationBuilder;
    taggedTemplateExpression: TaggedTemplateExpressionBuilder;
    templateLiteral: TemplateLiteralBuilder;
    templateElement: TemplateElementBuilder;
    metaProperty: MetaPropertyBuilder;
    awaitExpression: AwaitExpressionBuilder;
    spreadProperty: SpreadPropertyBuilder;
    spreadPropertyPattern: SpreadPropertyPatternBuilder;
    importExpression: ImportExpressionBuilder;
    chainExpression: ChainExpressionBuilder;
    optionalCallExpression: OptionalCallExpressionBuilder;
    optionalMemberExpression: OptionalMemberExpressionBuilder;
    jsxAttribute: JSXAttributeBuilder;
    jsxIdentifier: JSXIdentifierBuilder;
    jsxNamespacedName: JSXNamespacedNameBuilder;
    jsxExpressionContainer: JSXExpressionContainerBuilder;
    jsxElement: JSXElementBuilder;
    jsxFragment: JSXFragmentBuilder;
    jsxMemberExpression: JSXMemberExpressionBuilder;
    jsxSpreadAttribute: JSXSpreadAttributeBuilder;
    jsxEmptyExpression: JSXEmptyExpressionBuilder;
    jsxText: JSXTextBuilder;
    jsxSpreadChild: JSXSpreadChildBuilder;
    jsxOpeningElement: JSXOpeningElementBuilder;
    jsxClosingElement: JSXClosingElementBuilder;
    jsxOpeningFragment: JSXOpeningFragmentBuilder;
    jsxClosingFragment: JSXClosingFragmentBuilder;
    decorator: DecoratorBuilder;
    privateName: PrivateNameBuilder;
    classPrivateProperty: ClassPrivatePropertyBuilder;
    typeParameterDeclaration: TypeParameterDeclarationBuilder;
    tsTypeParameterDeclaration: TSTypeParameterDeclarationBuilder;
    typeParameterInstantiation: TypeParameterInstantiationBuilder;
    tsTypeParameterInstantiation: TSTypeParameterInstantiationBuilder;
    classImplements: ClassImplementsBuilder;
    tsExpressionWithTypeArguments: TSExpressionWithTypeArgumentsBuilder;
    anyTypeAnnotation: AnyTypeAnnotationBuilder;
    emptyTypeAnnotation: EmptyTypeAnnotationBuilder;
    mixedTypeAnnotation: MixedTypeAnnotationBuilder;
    voidTypeAnnotation: VoidTypeAnnotationBuilder;
    symbolTypeAnnotation: SymbolTypeAnnotationBuilder;
    numberTypeAnnotation: NumberTypeAnnotationBuilder;
    bigIntTypeAnnotation: BigIntTypeAnnotationBuilder;
    numberLiteralTypeAnnotation: NumberLiteralTypeAnnotationBuilder;
    numericLiteralTypeAnnotation: NumericLiteralTypeAnnotationBuilder;
    bigIntLiteralTypeAnnotation: BigIntLiteralTypeAnnotationBuilder;
    stringTypeAnnotation: StringTypeAnnotationBuilder;
    stringLiteralTypeAnnotation: StringLiteralTypeAnnotationBuilder;
    booleanTypeAnnotation: BooleanTypeAnnotationBuilder;
    booleanLiteralTypeAnnotation: BooleanLiteralTypeAnnotationBuilder;
    nullableTypeAnnotation: NullableTypeAnnotationBuilder;
    nullLiteralTypeAnnotation: NullLiteralTypeAnnotationBuilder;
    nullTypeAnnotation: NullTypeAnnotationBuilder;
    thisTypeAnnotation: ThisTypeAnnotationBuilder;
    existsTypeAnnotation: ExistsTypeAnnotationBuilder;
    existentialTypeParam: ExistentialTypeParamBuilder;
    functionTypeAnnotation: FunctionTypeAnnotationBuilder;
    functionTypeParam: FunctionTypeParamBuilder;
    arrayTypeAnnotation: ArrayTypeAnnotationBuilder;
    objectTypeAnnotation: ObjectTypeAnnotationBuilder;
    objectTypeProperty: ObjectTypePropertyBuilder;
    objectTypeSpreadProperty: ObjectTypeSpreadPropertyBuilder;
    objectTypeIndexer: ObjectTypeIndexerBuilder;
    objectTypeCallProperty: ObjectTypeCallPropertyBuilder;
    objectTypeInternalSlot: ObjectTypeInternalSlotBuilder;
    variance: VarianceBuilder;
    qualifiedTypeIdentifier: QualifiedTypeIdentifierBuilder;
    genericTypeAnnotation: GenericTypeAnnotationBuilder;
    memberTypeAnnotation: MemberTypeAnnotationBuilder;
    unionTypeAnnotation: UnionTypeAnnotationBuilder;
    intersectionTypeAnnotation: IntersectionTypeAnnotationBuilder;
    typeofTypeAnnotation: TypeofTypeAnnotationBuilder;
    typeParameter: TypeParameterBuilder;
    interfaceTypeAnnotation: InterfaceTypeAnnotationBuilder;
    interfaceExtends: InterfaceExtendsBuilder;
    interfaceDeclaration: InterfaceDeclarationBuilder;
    declareInterface: DeclareInterfaceBuilder;
    typeAlias: TypeAliasBuilder;
    declareTypeAlias: DeclareTypeAliasBuilder;
    opaqueType: OpaqueTypeBuilder;
    declareOpaqueType: DeclareOpaqueTypeBuilder;
    typeCastExpression: TypeCastExpressionBuilder;
    tupleTypeAnnotation: TupleTypeAnnotationBuilder;
    declareVariable: DeclareVariableBuilder;
    declareFunction: DeclareFunctionBuilder;
    declareClass: DeclareClassBuilder;
    declareModule: DeclareModuleBuilder;
    declareModuleExports: DeclareModuleExportsBuilder;
    declareExportDeclaration: DeclareExportDeclarationBuilder;
    exportBatchSpecifier: ExportBatchSpecifierBuilder;
    declareExportAllDeclaration: DeclareExportAllDeclarationBuilder;
    inferredPredicate: InferredPredicateBuilder;
    declaredPredicate: DeclaredPredicateBuilder;
    enumDeclaration: EnumDeclarationBuilder;
    enumBooleanBody: EnumBooleanBodyBuilder;
    enumNumberBody: EnumNumberBodyBuilder;
    enumStringBody: EnumStringBodyBuilder;
    enumSymbolBody: EnumSymbolBodyBuilder;
    enumBooleanMember: EnumBooleanMemberBuilder;
    enumNumberMember: EnumNumberMemberBuilder;
    enumStringMember: EnumStringMemberBuilder;
    enumDefaultedMember: EnumDefaultedMemberBuilder;
    exportDeclaration: ExportDeclarationBuilder;
    block: BlockBuilder;
    line: LineBuilder;
    noop: NoopBuilder;
    doExpression: DoExpressionBuilder;
    bindExpression: BindExpressionBuilder;
    parenthesizedExpression: ParenthesizedExpressionBuilder;
    exportNamespaceSpecifier: ExportNamespaceSpecifierBuilder;
    exportDefaultSpecifier: ExportDefaultSpecifierBuilder;
    commentBlock: CommentBlockBuilder;
    commentLine: CommentLineBuilder;
    directive: DirectiveBuilder;
    directiveLiteral: DirectiveLiteralBuilder;
    interpreterDirective: InterpreterDirectiveBuilder;
    stringLiteral: StringLiteralBuilder;
    numericLiteral: NumericLiteralBuilder;
    bigIntLiteral: BigIntLiteralBuilder;
    nullLiteral: NullLiteralBuilder;
    booleanLiteral: BooleanLiteralBuilder;
    regExpLiteral: RegExpLiteralBuilder;
    objectMethod: ObjectMethodBuilder;
    classMethod: ClassMethodBuilder;
    classPrivateMethod: ClassPrivateMethodBuilder;
    restProperty: RestPropertyBuilder;
    forAwaitStatement: ForAwaitStatementBuilder;
    import: ImportBuilder;
    tsQualifiedName: TSQualifiedNameBuilder;
    tsTypeReference: TSTypeReferenceBuilder;
    tsAsExpression: TSAsExpressionBuilder;
    tsNonNullExpression: TSNonNullExpressionBuilder;
    tsAnyKeyword: TSAnyKeywordBuilder;
    tsBigIntKeyword: TSBigIntKeywordBuilder;
    tsBooleanKeyword: TSBooleanKeywordBuilder;
    tsNeverKeyword: TSNeverKeywordBuilder;
    tsNullKeyword: TSNullKeywordBuilder;
    tsNumberKeyword: TSNumberKeywordBuilder;
    tsObjectKeyword: TSObjectKeywordBuilder;
    tsStringKeyword: TSStringKeywordBuilder;
    tsSymbolKeyword: TSSymbolKeywordBuilder;
    tsUndefinedKeyword: TSUndefinedKeywordBuilder;
    tsUnknownKeyword: TSUnknownKeywordBuilder;
    tsVoidKeyword: TSVoidKeywordBuilder;
    tsThisType: TSThisTypeBuilder;
    tsArrayType: TSArrayTypeBuilder;
    tsLiteralType: TSLiteralTypeBuilder;
    tsUnionType: TSUnionTypeBuilder;
    tsIntersectionType: TSIntersectionTypeBuilder;
    tsConditionalType: TSConditionalTypeBuilder;
    tsInferType: TSInferTypeBuilder;
    tsTypeParameter: TSTypeParameterBuilder;
    tsParenthesizedType: TSParenthesizedTypeBuilder;
    tsFunctionType: TSFunctionTypeBuilder;
    tsConstructorType: TSConstructorTypeBuilder;
    tsDeclareFunction: TSDeclareFunctionBuilder;
    tsDeclareMethod: TSDeclareMethodBuilder;
    tsMappedType: TSMappedTypeBuilder;
    tsTupleType: TSTupleTypeBuilder;
    tsNamedTupleMember: TSNamedTupleMemberBuilder;
    tsRestType: TSRestTypeBuilder;
    tsOptionalType: TSOptionalTypeBuilder;
    tsIndexedAccessType: TSIndexedAccessTypeBuilder;
    tsTypeOperator: TSTypeOperatorBuilder;
    tsIndexSignature: TSIndexSignatureBuilder;
    tsPropertySignature: TSPropertySignatureBuilder;
    tsMethodSignature: TSMethodSignatureBuilder;
    tsTypePredicate: TSTypePredicateBuilder;
    tsCallSignatureDeclaration: TSCallSignatureDeclarationBuilder;
    tsConstructSignatureDeclaration: TSConstructSignatureDeclarationBuilder;
    tsEnumMember: TSEnumMemberBuilder;
    tsTypeQuery: TSTypeQueryBuilder;
    tsImportType: TSImportTypeBuilder;
    tsTypeLiteral: TSTypeLiteralBuilder;
    tsTypeAssertion: TSTypeAssertionBuilder;
    tsEnumDeclaration: TSEnumDeclarationBuilder;
    tsTypeAliasDeclaration: TSTypeAliasDeclarationBuilder;
    tsModuleBlock: TSModuleBlockBuilder;
    tsModuleDeclaration: TSModuleDeclarationBuilder;
    tsImportEqualsDeclaration: TSImportEqualsDeclarationBuilder;
    tsExternalModuleReference: TSExternalModuleReferenceBuilder;
    tsExportAssignment: TSExportAssignmentBuilder;
    tsNamespaceExportDeclaration: TSNamespaceExportDeclarationBuilder;
    tsInterfaceBody: TSInterfaceBodyBuilder;
    tsInterfaceDeclaration: TSInterfaceDeclarationBuilder;
    tsParameterProperty: TSParameterPropertyBuilder;
};

/**
 * A plugin that updates source code
 * @public
 */
export declare type CodemodPlugin = {
    name: string;
    type: 'codemod';
    /**
     * Mutate the AST to make changes
     * @returns Number of transforms applied. Used for stats
     */
    transform(context: CodemodPluginContext): number;
};

/**
 * @public
 */
export declare type CodemodPluginContext = {
    /**
     * If this is a .vue file, the AST of the `<script>` blocks.
     * If this is a JS/TS module, the 0th element is the AST of the module
     */
    scriptASTs: VueProgram[];
    /**
     * If this is a .vue file, the AST of the SFC. Otherwise, null
     */
    sfcAST: AST.VDocumentFragment | null;
    /**
     * If this is a .vue file, postcss contexts of each \<style\> block
     * If this is a css/scss/less/sass/styl file, the 0th element is the context of the file
     */
    styleASTs: postcss.Root[];
    /**
     * The absolute path of the file being worked on
     */
    filename: string;
    /**
     * Utility functions
     */
    utils: typeof utils;
    /**
     * CLI Options
     */
    opts: Record<string, any>;
};

/**
 * Inserts a default import at the top of a script, or inserts a default import
 * on an existing import declaration for the moduleSpecifier
 * @param ast - The script AST
 * @param moduleSpecifier - The module name to import from
 * @param importName - The name of the default import
 */
declare function createDefaultImport(ast: namedTypes.Program, moduleSpecifier: string, importName: string): void;

/**
 * Inserts a named import at the top of a script, or inserts a named import on
 * an existing import declaration for the moduleSpecifier
 * @param ast - The script AST
 * @param moduleSpecifier - The module name to import from
 * @param importName - The name of the import
 * @param localName - (optional) The local name of the named import
 */
declare function createNamedImport(ast: namedTypes.Program, moduleSpecifier: string, importName: string, localName?: string): void;

/**
 * Inserts a namespaced import at the top of a script
 * @param ast - The script AST
 * @param moduleSpecifier - The module name to import from
 * @param namespaceName - The name of the namespace in the module
 */
declare function createNamespaceImport(ast: namedTypes.Program, moduleSpecifier: string, namespaceName: string): void;

/**
 * Creates a CLI instance
 * @public
 */
export declare function createVueMetamorphCli(options: CreateVueMetamorphCliOptions): {
    /**
     * Run the CLI
     */
    run: (argv?: string[]) => Promise<void>;
    /**
     * Stops progress of the runner
     */
    abort: () => void;
    /**
     * Commander arguments
     */
    opts: (argv?: string[]) => OptionValues;
};

/**
 * vue-metamorph CLI Options
 * @public
 */
export declare interface CreateVueMetamorphCliOptions {
    /**
     * Whether to suppress the default output of vue-metamorph's CLI
     *
     * If you set this to true, use the onProgress function to define your own output
     */
    silent?: boolean;
    /**
     * The vue-metamorph CLI will call this function when a file has been transformed
     * and written back to disk
     */
    onProgress?: ProgressCallback;
    /**
     * List of codemods / manual migrations to run against matching files
     */
    plugins: (Plugin | Plugin[])[];
    /**
     * Add additional commander options to access with opts()
     * @param program - Commander Command
     */
    additionalCliOptions?: (program: Pick<Command, 'option' | 'requiredOption'>) => void;
}

/**
 * An error that was encountered during parsing or plugin execution
 * @public
 */
export declare type ErrorReport = {
    /**
     * The error object that was thrown
     */
    error: Error;
    /**
     * The filename that was being processed when the error was thrown
     */
    filename: string;
};

declare function findAll<M extends Matcher<namedTypes.ASTNode | AST.Node>>(ast: AST.Node | namedTypes.ASTNode, matcher: M): (AST.Node & {
    type: M['type'];
})[];

/**
 * Finds the first node in an AST that matches a partial node
 * @param ast - The node to traverse
 * @param matcher - Partial object to match against
 * @returns The first matching node, or null if no matching node was found
 */
declare function findFirst<M extends Matcher<namedTypes.ASTNode | AST.Node>>(ast: AST.Node | namedTypes.ASTNode, matcher: M): (AST.Node & {
    type: M['type'];
}) | null;

/**
 * Finds an existing import declaration for a module
 * @param ast - The script AST
 * @param moduleSpecifier - The module name
 * @returns The ImportDeclaration node if one was found, or null
 */
declare function findImportDeclaration(ast: namedTypes.Program, moduleSpecifier: string): namedTypes.ImportDeclaration | null;

/**
 * Finds manual migration locations in a file
 * @param code - Source code
 * @param filename - The file name
 * @param plugins - Manual migration plugins
 * @param opts - CLI Options
 * @returns List of reports
 * @public
 */
export declare function findManualMigrations(code: string, filename: string, plugins: ManualMigrationPlugin[], opts?: Record<string, any>): ManualMigrationReport[];

/**
 * Finds the Options API objects passed to Vue.extend(), Vue.component(),
 * Vue.mixin(), defineComponent(), or new Vue()
 * @param ast - The script AST
 * @param isSfc - If true, treat the default export as an options api object
 */
declare function findVueComponentOptions(ast: namedTypes.Program, isSfc: boolean): namedTypes.ObjectExpression[];

/**
 * @public
 */
declare interface HasParent {
    parent?: Node | null;
}

export { Kinds }

/**
 * A plugin for finding nodes that cannot be migrated automatically
 *
 * @public
 */
export declare type ManualMigrationPlugin = {
    type: 'manual';
    name: string;
    /**
     * Find nodes that need manual migration
     */
    find(context: ManualMigrationPluginContext): void;
};

/**
 * @public
 */
export declare type ManualMigrationPluginContext = {
    /**
     * If this is a .vue file, the AST of the \<script\> blocks.
     * If this is a JS/TS module, the 0th element is the AST of the module
     */
    scriptASTs: VueProgram[];
    /**
     * If this is a .vue file, the AST of the SFC. Otherwise, null
     */
    sfcAST: AST.VDocumentFragment | null;
    /**
     * If this is a .vue file, postcss contexts of each \<style\> block
     * If this is a css/scss/less/sass/styl file, the 0th element is the context of the file
     */
    styleASTs: postcss.Root[];
    /**
     * The absolute path of the file being worked on
     */
    filename: string;
    /**
     * Function to report a node that needs to be migrated
     */
    report: ReportFunction;
    /**
     * Utility functions
     */
    utils: typeof utils;
    /**
     * CLI Options
     */
    opts: Record<string, any>;
};

/**
 * An object containing a manual migration that a ManualMigrationPlugin reported
 * @public
 */
export declare type ManualMigrationReport = {
    /**
     * Migration message
     */
    message: string;
    /**
     * The filename
     */
    file: string;
    /**
     * Code snippet highlighting the node
     */
    snippet: string;
    /**
     * The plugin that generated this report
     */
    pluginName: string;
    /**
     * 1-based index of the starting line number
     */
    lineStart: number;
    /**
     * 1-based index of the ending line number
     */
    lineEnd: number;
    /**
     * 1-based index of the starting column number on the starting line
     */
    columnStart: number;
    /**
     * 1-based index of the enging column number on the ending line
     */
    columnEnd: number;
};

declare type Matcher<T> = T extends {
    type: string;
} ? {
    [key in Exclude<keyof T, 'type' | 'comments' | 'loc' | 'range'>]?: NonNullable<T[key]> extends (infer A)[] ? Matcher<A>[] : Matcher<T[key]>;
} & {
    type: T['type'];
} : T;

export { namedTypes }

/**
 * Type of namespaces.
 * @public
 */
declare type Namespace = typeof NS.HTML | typeof NS.MathML | typeof NS.SVG | typeof NS.XLink | typeof NS.XML | typeof NS.XMLNS;

/**
 * The union type for all nodes.
 * @public
 */
declare type Node = Exclude<namedTypes.ASTNode & HasParent, namedTypes.Program | namedTypes.File> | VNode | VForExpression | VOnExpression | VSlotScopeExpression | VFilterSequenceExpression | VFilter | VDocumentFragment;

/**
 * @public
 */
declare const NS: Readonly<{
    HTML: "http://www.w3.org/1999/xhtml";
    MathML: "http://www.w3.org/1998/Math/MathML";
    SVG: "http://www.w3.org/2000/svg";
    XLink: "http://www.w3.org/1999/xlink";
    XML: "http://www.w3.org/XML/1998/namespace";
    XMLNS: "http://www.w3.org/2000/xmlns/";
}>;

/**
 * Union of plugin types
 * @public
 */
export declare type Plugin = ManualMigrationPlugin | CodemodPlugin;

export { postcss }

/**
 * Function signature for the onProgress function passed to createVueMetamorphCli
 * @public
 */
export declare type ProgressCallback = (args: {
    /**
     * Total number files matching the glob input
     */
    totalFiles: number;
    /**
     * Number of files that have already been processed
     */
    filesProcessed: number;
    /**
     * Number of files left to be processed
     */
    filesRemaining: number;
    /**
     * Number of changes each plugin has applied or reported
     */
    stats: Record<string, number>;
    /**
     * True if the runner was aborted before completing
     */
    aborted: boolean;
    /**
     * True if the runner finished processing all files
     */
    done: boolean;
    /**
     * Errors encountered during processing
     */
    errors: ErrorReport[];
    /**
     * Manual migrations reported by manual migration plugins
     */
    manualMigrations: ManualMigrationReport[];
}) => void;

declare type ReportFunction = (node: AST.Node | postcss.AnyNode, message: string) => void;

/**
 * Runs codemods against source code
 * @param code - Source code
 * @param filename - The file name, used to determine whether to parse as JS/TS, or as a .vue SFC
 * @param plugins - List of codemod plugins
 * @param opts - CLI Options
 * @returns New source code
 * @public
 */
export declare function transform(code: string, filename: string, plugins: CodemodPlugin[], opts?: Record<string, any>): TransformResult;

/**
 * Return type of the `transform` function, containing new source code and codemod stats
 * @public
 */
export declare type TransformResult = {
    /**
     * The new source code
     */
    code: string;
    /**
     * Stats on how many transforms each codemod reported that it made
     */
    stats: [codemodName: string, transformCount: number][];
};

/**
 * @public
 */
declare const traverseNodes: (node: Node, visitor: {
    enterNode?(node: Node, parent: Node | null): void;
    leaveNode?(node: Node, parent: Node | null): void;
}) => void;

export { traverseScriptAST }

declare const utils: {
    traverseTemplateAST: (node: AST.Node, visitor: {
        enterNode?(node: AST.Node, parent: AST.Node | null): void;
        leaveNode?(node: AST.Node, parent: AST.Node | null): void;
    }) => void;
    traverseScriptAST: <M = {}>(node: ASTNode, methods?: Visitor<M> | undefined) => any;
    builders: {
        file: FileBuilder;
        program: ProgramBuilder;
        identifier: IdentifierBuilder;
        blockStatement: BlockStatementBuilder;
        emptyStatement: EmptyStatementBuilder;
        expressionStatement: ExpressionStatementBuilder;
        ifStatement: IfStatementBuilder;
        labeledStatement: LabeledStatementBuilder;
        breakStatement: BreakStatementBuilder;
        continueStatement: ContinueStatementBuilder;
        withStatement: WithStatementBuilder;
        switchStatement: SwitchStatementBuilder;
        switchCase: SwitchCaseBuilder;
        returnStatement: ReturnStatementBuilder;
        throwStatement: ThrowStatementBuilder;
        tryStatement: TryStatementBuilder;
        catchClause: CatchClauseBuilder;
        whileStatement: WhileStatementBuilder;
        doWhileStatement: DoWhileStatementBuilder;
        forStatement: ForStatementBuilder;
        variableDeclaration: VariableDeclarationBuilder;
        forInStatement: ForInStatementBuilder;
        debuggerStatement: DebuggerStatementBuilder;
        functionDeclaration: FunctionDeclarationBuilder;
        functionExpression: FunctionExpressionBuilder;
        variableDeclarator: VariableDeclaratorBuilder;
        thisExpression: ThisExpressionBuilder;
        arrayExpression: ArrayExpressionBuilder;
        objectExpression: ObjectExpressionBuilder;
        property: PropertyBuilder;
        literal: LiteralBuilder;
        sequenceExpression: SequenceExpressionBuilder;
        unaryExpression: UnaryExpressionBuilder;
        binaryExpression: BinaryExpressionBuilder;
        assignmentExpression: AssignmentExpressionBuilder;
        memberExpression: MemberExpressionBuilder;
        updateExpression: UpdateExpressionBuilder;
        logicalExpression: LogicalExpressionBuilder;
        conditionalExpression: ConditionalExpressionBuilder;
        newExpression: NewExpressionBuilder;
        callExpression: CallExpressionBuilder;
        restElement: RestElementBuilder;
        typeAnnotation: TypeAnnotationBuilder;
        tsTypeAnnotation: TSTypeAnnotationBuilder;
        spreadElementPattern: SpreadElementPatternBuilder;
        arrowFunctionExpression: ArrowFunctionExpressionBuilder;
        forOfStatement: ForOfStatementBuilder;
        yieldExpression: YieldExpressionBuilder;
        generatorExpression: GeneratorExpressionBuilder;
        comprehensionBlock: ComprehensionBlockBuilder;
        comprehensionExpression: ComprehensionExpressionBuilder;
        objectProperty: ObjectPropertyBuilder;
        propertyPattern: PropertyPatternBuilder;
        objectPattern: ObjectPatternBuilder;
        arrayPattern: ArrayPatternBuilder;
        spreadElement: SpreadElementBuilder;
        assignmentPattern: AssignmentPatternBuilder;
        methodDefinition: MethodDefinitionBuilder;
        classPropertyDefinition: ClassPropertyDefinitionBuilder;
        classProperty: ClassPropertyBuilder;
        classBody: ClassBodyBuilder;
        classDeclaration: ClassDeclarationBuilder;
        classExpression: ClassExpressionBuilder;
        super: SuperBuilder;
        importSpecifier: ImportSpecifierBuilder;
        importDefaultSpecifier: ImportDefaultSpecifierBuilder;
        importNamespaceSpecifier: ImportNamespaceSpecifierBuilder;
        importDeclaration: ImportDeclarationBuilder;
        exportNamedDeclaration: ExportNamedDeclarationBuilder;
        exportSpecifier: ExportSpecifierBuilder;
        exportDefaultDeclaration: ExportDefaultDeclarationBuilder;
        exportAllDeclaration: ExportAllDeclarationBuilder;
        taggedTemplateExpression: TaggedTemplateExpressionBuilder;
        templateLiteral: TemplateLiteralBuilder;
        templateElement: TemplateElementBuilder;
        metaProperty: MetaPropertyBuilder;
        awaitExpression: AwaitExpressionBuilder;
        spreadProperty: SpreadPropertyBuilder;
        spreadPropertyPattern: SpreadPropertyPatternBuilder;
        importExpression: ImportExpressionBuilder;
        chainExpression: ChainExpressionBuilder;
        optionalCallExpression: OptionalCallExpressionBuilder;
        optionalMemberExpression: OptionalMemberExpressionBuilder;
        jsxAttribute: JSXAttributeBuilder;
        jsxIdentifier: JSXIdentifierBuilder;
        jsxNamespacedName: JSXNamespacedNameBuilder;
        jsxExpressionContainer: JSXExpressionContainerBuilder;
        jsxElement: JSXElementBuilder;
        jsxFragment: JSXFragmentBuilder;
        jsxMemberExpression: JSXMemberExpressionBuilder;
        jsxSpreadAttribute: JSXSpreadAttributeBuilder;
        jsxEmptyExpression: JSXEmptyExpressionBuilder;
        jsxText: JSXTextBuilder;
        jsxSpreadChild: JSXSpreadChildBuilder;
        jsxOpeningElement: JSXOpeningElementBuilder;
        jsxClosingElement: JSXClosingElementBuilder;
        jsxOpeningFragment: JSXOpeningFragmentBuilder;
        jsxClosingFragment: JSXClosingFragmentBuilder;
        decorator: DecoratorBuilder;
        privateName: PrivateNameBuilder;
        classPrivateProperty: ClassPrivatePropertyBuilder;
        typeParameterDeclaration: TypeParameterDeclarationBuilder;
        tsTypeParameterDeclaration: TSTypeParameterDeclarationBuilder;
        typeParameterInstantiation: TypeParameterInstantiationBuilder;
        tsTypeParameterInstantiation: TSTypeParameterInstantiationBuilder;
        classImplements: ClassImplementsBuilder;
        tsExpressionWithTypeArguments: TSExpressionWithTypeArgumentsBuilder;
        anyTypeAnnotation: AnyTypeAnnotationBuilder;
        emptyTypeAnnotation: EmptyTypeAnnotationBuilder;
        mixedTypeAnnotation: MixedTypeAnnotationBuilder;
        voidTypeAnnotation: VoidTypeAnnotationBuilder;
        symbolTypeAnnotation: SymbolTypeAnnotationBuilder;
        numberTypeAnnotation: NumberTypeAnnotationBuilder;
        bigIntTypeAnnotation: BigIntTypeAnnotationBuilder;
        numberLiteralTypeAnnotation: NumberLiteralTypeAnnotationBuilder;
        numericLiteralTypeAnnotation: NumericLiteralTypeAnnotationBuilder;
        bigIntLiteralTypeAnnotation: BigIntLiteralTypeAnnotationBuilder;
        stringTypeAnnotation: StringTypeAnnotationBuilder;
        stringLiteralTypeAnnotation: StringLiteralTypeAnnotationBuilder;
        booleanTypeAnnotation: BooleanTypeAnnotationBuilder;
        booleanLiteralTypeAnnotation: BooleanLiteralTypeAnnotationBuilder;
        nullableTypeAnnotation: NullableTypeAnnotationBuilder;
        nullLiteralTypeAnnotation: NullLiteralTypeAnnotationBuilder;
        nullTypeAnnotation: NullTypeAnnotationBuilder;
        thisTypeAnnotation: ThisTypeAnnotationBuilder;
        existsTypeAnnotation: ExistsTypeAnnotationBuilder;
        existentialTypeParam: ExistentialTypeParamBuilder;
        functionTypeAnnotation: FunctionTypeAnnotationBuilder;
        functionTypeParam: FunctionTypeParamBuilder;
        arrayTypeAnnotation: ArrayTypeAnnotationBuilder;
        objectTypeAnnotation: ObjectTypeAnnotationBuilder;
        objectTypeProperty: ObjectTypePropertyBuilder;
        objectTypeSpreadProperty: ObjectTypeSpreadPropertyBuilder;
        objectTypeIndexer: ObjectTypeIndexerBuilder;
        objectTypeCallProperty: ObjectTypeCallPropertyBuilder;
        objectTypeInternalSlot: ObjectTypeInternalSlotBuilder;
        variance: VarianceBuilder;
        qualifiedTypeIdentifier: QualifiedTypeIdentifierBuilder;
        genericTypeAnnotation: GenericTypeAnnotationBuilder;
        memberTypeAnnotation: MemberTypeAnnotationBuilder;
        unionTypeAnnotation: UnionTypeAnnotationBuilder;
        intersectionTypeAnnotation: IntersectionTypeAnnotationBuilder;
        typeofTypeAnnotation: TypeofTypeAnnotationBuilder;
        typeParameter: TypeParameterBuilder;
        interfaceTypeAnnotation: InterfaceTypeAnnotationBuilder;
        interfaceExtends: InterfaceExtendsBuilder;
        interfaceDeclaration: InterfaceDeclarationBuilder;
        declareInterface: DeclareInterfaceBuilder;
        typeAlias: TypeAliasBuilder;
        declareTypeAlias: DeclareTypeAliasBuilder;
        opaqueType: OpaqueTypeBuilder;
        declareOpaqueType: DeclareOpaqueTypeBuilder;
        typeCastExpression: TypeCastExpressionBuilder;
        tupleTypeAnnotation: TupleTypeAnnotationBuilder;
        declareVariable: DeclareVariableBuilder;
        declareFunction: DeclareFunctionBuilder;
        declareClass: DeclareClassBuilder;
        declareModule: DeclareModuleBuilder;
        declareModuleExports: DeclareModuleExportsBuilder;
        declareExportDeclaration: DeclareExportDeclarationBuilder;
        exportBatchSpecifier: ExportBatchSpecifierBuilder;
        declareExportAllDeclaration: DeclareExportAllDeclarationBuilder;
        inferredPredicate: InferredPredicateBuilder;
        declaredPredicate: DeclaredPredicateBuilder;
        enumDeclaration: EnumDeclarationBuilder;
        enumBooleanBody: EnumBooleanBodyBuilder;
        enumNumberBody: EnumNumberBodyBuilder;
        enumStringBody: EnumStringBodyBuilder;
        enumSymbolBody: EnumSymbolBodyBuilder;
        enumBooleanMember: EnumBooleanMemberBuilder;
        enumNumberMember: EnumNumberMemberBuilder;
        enumStringMember: EnumStringMemberBuilder;
        enumDefaultedMember: EnumDefaultedMemberBuilder;
        exportDeclaration: ExportDeclarationBuilder;
        block: BlockBuilder;
        line: LineBuilder;
        noop: NoopBuilder;
        doExpression: DoExpressionBuilder;
        bindExpression: BindExpressionBuilder;
        parenthesizedExpression: ParenthesizedExpressionBuilder;
        exportNamespaceSpecifier: ExportNamespaceSpecifierBuilder;
        exportDefaultSpecifier: ExportDefaultSpecifierBuilder;
        commentBlock: CommentBlockBuilder;
        commentLine: CommentLineBuilder;
        directive: DirectiveBuilder;
        directiveLiteral: DirectiveLiteralBuilder;
        interpreterDirective: InterpreterDirectiveBuilder;
        stringLiteral: StringLiteralBuilder;
        numericLiteral: NumericLiteralBuilder;
        bigIntLiteral: BigIntLiteralBuilder;
        nullLiteral: NullLiteralBuilder;
        booleanLiteral: BooleanLiteralBuilder;
        regExpLiteral: RegExpLiteralBuilder;
        objectMethod: ObjectMethodBuilder;
        classMethod: ClassMethodBuilder;
        classPrivateMethod: ClassPrivateMethodBuilder;
        restProperty: RestPropertyBuilder;
        forAwaitStatement: ForAwaitStatementBuilder;
        import: ImportBuilder;
        tsQualifiedName: TSQualifiedNameBuilder;
        tsTypeReference: TSTypeReferenceBuilder;
        tsAsExpression: TSAsExpressionBuilder;
        tsNonNullExpression: TSNonNullExpressionBuilder;
        tsAnyKeyword: TSAnyKeywordBuilder;
        tsBigIntKeyword: TSBigIntKeywordBuilder;
        tsBooleanKeyword: TSBooleanKeywordBuilder;
        tsNeverKeyword: TSNeverKeywordBuilder;
        tsNullKeyword: TSNullKeywordBuilder;
        tsNumberKeyword: TSNumberKeywordBuilder;
        tsObjectKeyword: TSObjectKeywordBuilder;
        tsStringKeyword: TSStringKeywordBuilder;
        tsSymbolKeyword: TSSymbolKeywordBuilder;
        tsUndefinedKeyword: TSUndefinedKeywordBuilder;
        tsUnknownKeyword: TSUnknownKeywordBuilder;
        tsVoidKeyword: TSVoidKeywordBuilder;
        tsThisType: TSThisTypeBuilder;
        tsArrayType: TSArrayTypeBuilder;
        tsLiteralType: TSLiteralTypeBuilder;
        tsUnionType: TSUnionTypeBuilder;
        tsIntersectionType: TSIntersectionTypeBuilder;
        tsConditionalType: TSConditionalTypeBuilder;
        tsInferType: TSInferTypeBuilder;
        tsTypeParameter: TSTypeParameterBuilder;
        tsParenthesizedType: TSParenthesizedTypeBuilder;
        tsFunctionType: TSFunctionTypeBuilder;
        tsConstructorType: TSConstructorTypeBuilder;
        tsDeclareFunction: TSDeclareFunctionBuilder;
        tsDeclareMethod: TSDeclareMethodBuilder;
        tsMappedType: TSMappedTypeBuilder;
        tsTupleType: TSTupleTypeBuilder;
        tsNamedTupleMember: TSNamedTupleMemberBuilder;
        tsRestType: TSRestTypeBuilder;
        tsOptionalType: TSOptionalTypeBuilder;
        tsIndexedAccessType: TSIndexedAccessTypeBuilder;
        tsTypeOperator: TSTypeOperatorBuilder;
        tsIndexSignature: TSIndexSignatureBuilder;
        tsPropertySignature: TSPropertySignatureBuilder;
        tsMethodSignature: TSMethodSignatureBuilder;
        tsTypePredicate: TSTypePredicateBuilder;
        tsCallSignatureDeclaration: TSCallSignatureDeclarationBuilder;
        tsConstructSignatureDeclaration: TSConstructSignatureDeclarationBuilder;
        tsEnumMember: TSEnumMemberBuilder;
        tsTypeQuery: TSTypeQueryBuilder;
        tsImportType: TSImportTypeBuilder;
        tsTypeLiteral: TSTypeLiteralBuilder;
        tsTypeAssertion: TSTypeAssertionBuilder;
        tsEnumDeclaration: TSEnumDeclarationBuilder;
        tsTypeAliasDeclaration: TSTypeAliasDeclarationBuilder;
        tsModuleBlock: TSModuleBlockBuilder;
        tsModuleDeclaration: TSModuleDeclarationBuilder;
        tsImportEqualsDeclaration: TSImportEqualsDeclarationBuilder;
        tsExternalModuleReference: TSExternalModuleReferenceBuilder;
        tsExportAssignment: TSExportAssignmentBuilder;
        tsNamespaceExportDeclaration: TSNamespaceExportDeclarationBuilder;
        tsInterfaceBody: TSInterfaceBodyBuilder;
        tsInterfaceDeclaration: TSInterfaceDeclarationBuilder;
        tsParameterProperty: TSParameterPropertyBuilder;
        setParents(node: AST.Node): void;
        vAttribute(key: AST.VAttribute["key"], value: AST.VAttribute["value"]): AST.VAttribute;
        vDirective(key: AST.VDirective["key"], value: AST.VDirective["value"]): AST.VDirective;
        vDirectiveKey(name: AST.VDirectiveKey["name"], argument?: AST.VDirectiveKey["argument"], modifiers?: AST.VDirectiveKey["modifiers"]): AST.VDirectiveKey;
        vDocumentFragment(children: AST.VDocumentFragment["children"]): AST.VDocumentFragment;
        vEndTag(): AST.VEndTag;
        vElement(name: string, startTag: AST.VStartTag, children: AST.VElement["children"], namespace?: AST.VElement["namespace"]): AST.VElement;
        vExpressionContainer(expression: AST.VExpressionContainer["expression"]): AST.VExpressionContainer;
        vForExpression(left: AST.VForExpression["left"], right: AST.VForExpression["right"]): AST.VForExpression;
        vIdentifier(name: AST.VIdentifier["name"], rawName?: AST.VIdentifier["rawName"]): AST.VIdentifier;
        vLiteral(value: AST.VLiteral["value"]): AST.VLiteral;
        vStartTag(attributes: AST.VStartTag["attributes"], selfClosing: AST.VStartTag["selfClosing"]): AST.VStartTag;
        vText(value: AST.VText["value"]): AST.VText;
        vOnExpression(body: AST.VOnExpression["body"]): AST.VOnExpression;
        vFilterSequenceExpression(expression: AST.VFilterSequenceExpression["expression"], filters: AST.VFilterSequenceExpression["filters"]): AST.VFilterSequenceExpression;
        vFilter(callee: AST.VFilter["callee"], args: AST.VFilter["arguments"]): AST.VFilter;
    };
    astHelpers: typeof astHelpers;
};

/**
 * Static attribute nodes.
 * @public
 */
declare interface VAttribute extends HasParent {
    type: 'VAttribute';
    parent: VStartTag;
    directive: false;
    key: VIdentifier;
    value: VLiteral | null;
}

/**
 * Directive nodes.
 * @public
 */
declare interface VDirective extends HasParent {
    type: 'VAttribute';
    parent: VStartTag;
    directive: true;
    key: VDirectiveKey;
    value: VExpressionContainer | null;
}

/**
 * Attribute name nodes.
 * @public
 */
declare interface VDirectiveKey extends HasParent {
    type: 'VDirectiveKey';
    parent: VDirective;
    name: VIdentifier;
    argument: VExpressionContainer | VIdentifier | null;
    modifiers: VIdentifier[];
}

/**
 * Root nodes.
 * @public
 */
declare interface VDocumentFragment extends HasParent {
    type: 'VDocumentFragment';
    parent: null;
    children: (VElement | VText | VExpressionContainer | VStyleElement)[];
}

/**
 * Element nodes.
 * @public
 */
declare interface VElement extends HasParent {
    type: 'VElement';
    parent: VDocumentFragment | VElement;
    namespace: Namespace;
    name: string;
    rawName: string;
    startTag: VStartTag;
    children: (VElement | VText | VExpressionContainer)[];
    endTag: VEndTag | null;
}

/**
 * End tag nodes.
 * @public
 */
declare interface VEndTag extends HasParent {
    type: 'VEndTag';
    parent: VElement;
}

/**
 * The node of JavaScript expression in text.
 * e.g. `{{ name }}`
 * @public
 */
declare interface VExpressionContainer extends HasParent {
    type: 'VExpressionContainer';
    parent: VDocumentFragment | VElement | VDirective | VDirectiveKey;
    expression: ExpressionKind | PatternKind | VFilterSequenceExpression | VForExpression | VOnExpression | VSlotScopeExpression | VGenericExpression | null;
}

/**
 * The node of a filter sequence which is separated by `|`.
 * @public
 */
declare interface VFilter extends HasParent {
    type: 'VFilter';
    parent: VFilterSequenceExpression;
    callee: namedTypes.Identifier;
    arguments: (ExpressionKind | namedTypes.SpreadElement)[];
}

/**
 * The node of a filter sequence which is separated by `|`.
 * @public
 */
declare interface VFilterSequenceExpression extends HasParent {
    type: 'VFilterSequenceExpression';
    parent: VExpressionContainer;
    expression: ExpressionKind;
    filters: VFilter[];
}

/**
 * The node of `v-for` directives.
 * @public
 */
declare interface VForExpression extends HasParent {
    type: 'VForExpression';
    parent: VExpressionContainer;
    left: PatternKind[];
    right: ExpressionKind;
}

/**
 * The generic expression on a \<script setup lang="ts" generic="..."\> node
 * @public
 */
declare interface VGenericExpression extends HasParent {
    type: 'VGenericExpression';
    parent: VExpressionContainer;
    params: namedTypes.TSTypeParameterDeclaration['params'];
    rawParams: string[];
}

/**
 * Attribute name nodes.
 * @public
 */
declare interface VIdentifier extends HasParent {
    type: 'VIdentifier';
    parent: VAttribute | VDirectiveKey;
    name: string;
    rawName: string;
}

/**
 * Attribute value nodes.
 * @public
 */
declare interface VLiteral extends HasParent {
    type: 'VLiteral';
    parent: VAttribute;
    value: string;
}

/**
 * The union type of any nodes.
 * @public
 */
declare type VNode = VAttribute | VDirective | VDirectiveKey | VDocumentFragment | VElement | VEndTag | VExpressionContainer | VIdentifier | VLiteral | VStartTag | VText | VGenericExpression;

/**
 * The node of `v-on` directives.
 * @public
 */
declare interface VOnExpression extends HasParent {
    type: 'VOnExpression';
    parent: VExpressionContainer;
    body: StatementKind[];
}

/**
 * The node of `slot-scope` directives.
 * @public
 */
declare interface VSlotScopeExpression extends HasParent {
    type: 'VSlotScopeExpression';
    parent: VExpressionContainer;
    params: PatternKind[];
}

/**
 * Start tag nodes.
 * @public
 */
declare interface VStartTag extends HasParent {
    type: 'VStartTag';
    parent: VElement;
    selfClosing: boolean;
    attributes: (VAttribute | VDirective)[];
}

/**
 * Style element nodes.
 * @public
 */
declare interface VStyleElement extends VElement {
    type: 'VElement';
    name: 'style';
    style: true;
    children: (VText | VExpressionContainer)[];
}

/**
 * Text nodes.
 * @public
 */
declare interface VText extends HasParent {
    type: 'VText';
    parent: VDocumentFragment | VElement;
    value: string;
}

/**
 * ESTree Program type, with an additional property `isScriptSetup` that denotes whether the program
 * represents the contents of a \<script setup\> block in a Vue SFC
 *
 * @public
 */
export declare type VueProgram = namedTypes.Program & {
    /**
     * Whether this Program represents the contents of a \<script setup\>
     *
     * In a JS/TS file, this will always be false.
     */
    isScriptSetup: boolean;
};

export { }
