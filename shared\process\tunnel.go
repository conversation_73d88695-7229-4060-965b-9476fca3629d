package process

import (
	"context"
	"fmt"

	cfg "github.com/csee-pm/etl/shared/config"
	ctx "github.com/csee-pm/etl/shared/context"
	log "github.com/csee-pm/etl/shared/logger"
	"github.com/likearthian/go-sshtun"
	"github.com/likearthian/go-sshtun/auth"
)

func UseTunnelIfRequired(c context.Context, tunnelConfigKey string) error {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	tunnel := conf.Get(tunnelConfigKey)
	var useTunnel = false
	if tunnel != nil {
		useTunnel = true
	}

	if useTunnel {
		tunConfig, err := cfg.GetTunnelConfig(conf, tunnelConfigKey)
		if err != nil {
			return fmt.Errorf("failed to get tunnel config. %s", err)
		}

		if err := StartTunnel(tunConfig, logger); err != nil {
			return fmt.Errorf("failed to start tunnel. %s", err)
		}
	}

	return nil
}

func StartTunnel(cf cfg.TunnelConfig, logger log.Logger) error {
	tun, err := CreateTunnel(cf)
	if err != nil {
		return err
	}

	err = tun.Start()
	if err != nil {
		return err
	}

	var logf func(string, ...any)
	go func() {
		defer tun.Close()
		for msg := range tun.ConnState() {
			strMsg := msg.Msg
			logf = logger.Info
			if msg.Err != nil {
				strMsg = msg.Err.Error()
				logf = logger.Error
			}

			if msg.State != sshtun.ConnStateKeepAlive {
				logf(strMsg)
			}
		}
	}()

	return nil
}

func CreateTunnel(cf cfg.TunnelConfig) (*sshtun.SSHTunnel, error) {
	sshPort := cf.Ssh.Port
	if sshPort == 0 {
		sshPort = 22
	}

	tun, err := sshtun.NewSSHTunnel(
		sshtun.SSHConfig{
			User: cf.Ssh.User,
			Host: cf.Ssh.Host,
			Port: sshPort,
			Auth: auth.PrivateKeyFile(cf.Ssh.PrivateKeyFile),
		},
		cf.LocalPort,
		cf.Destination,
	)

	if err != nil {
		return nil, err
	}

	return tun, nil
}
