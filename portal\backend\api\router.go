package api

import (
	"encoding/json"
	"net/http"
	"strconv"
	"strings"

	"github.com/csee-pm/etl/portal/backend/config"
	"github.com/csee-pm/etl/portal/backend/db"
	"github.com/gorilla/mux"
)

// Router handles HTTP requests
type Router struct {
	db     *db.DB
	config *config.Config
	router *mux.Router
}

// NewRouter creates a new router
func NewRouter(database *db.DB, cfg *config.Config) *Router {
	r := &Router{
		db:     database,
		config: cfg,
		router: mux.NewRouter(),
	}

	// Register routes
	r.registerRoutes()

	return r
}

// ServeHTTP implements the http.Handler interface
func (r *Router) ServeHTTP(w http.ResponseWriter, req *http.Request) {
	// Set CORS headers
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

	// Handle preflight requests
	if req.Method == http.MethodOptions {
		w.WriteHeader(http.StatusOK)
		return
	}

	// Serve static files for the frontend
	if strings.HasPrefix(req.URL.Path, "/assets/") || req.URL.Path == "/" || req.URL.Path == "/index.html" {
		http.FileServer(http.Dir("../frontend/dist")).ServeHTTP(w, req)
		return
	}

	// Handle API requests
	r.router.ServeHTTP(w, req)
}

// registerRoutes registers all API routes
func (r *Router) registerRoutes() {
	// API routes
	api := r.router.PathPrefix("/api").Subrouter()

	// Process routes
	api.HandleFunc("/processes", r.handleListProcesses).Methods("GET")
	api.HandleFunc("/processes/{id:[0-9]+}", r.handleGetProcess).Methods("GET")
	api.HandleFunc("/processes", r.handleCreateProcess).Methods("POST")
	api.HandleFunc("/processes/{id:[0-9]+}", r.handleUpdateProcess).Methods("PUT")

	// File routes
	api.HandleFunc("/files", r.handleListFiles).Methods("GET")
	api.HandleFunc("/files/{id:[0-9]+}", r.handleGetFile).Methods("GET")
	api.HandleFunc("/processes/{id:[0-9]+}/files", r.handleListProcessFiles).Methods("GET")
	api.HandleFunc("/files/{id:[0-9]+}", r.handleDeleteFile).Methods("DELETE")

	// ETL control routes
	api.HandleFunc("/etl/start", r.handleStartETL).Methods("POST")
	api.HandleFunc("/etl/stop/{id:[0-9]+}", r.handleStopETL).Methods("POST")
}

// Helper functions for handling requests

// respondJSON sends a JSON response
func respondJSON(w http.ResponseWriter, status int, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)

	if data != nil {
		if err := json.NewEncoder(w).Encode(data); err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
		}
	}
}

// respondError sends an error response
func respondError(w http.ResponseWriter, status int, message string) {
	respondJSON(w, status, map[string]string{"error": message})
}

// getIDParam extracts the ID parameter from the URL
func getIDParam(r *http.Request, paramName string) (int64, error) {
	vars := mux.Vars(r)
	idStr, ok := vars[paramName]
	if !ok {
		return 0, nil
	}
	return strconv.ParseInt(idStr, 10, 64)
}
