package config

import (
	"fmt"
	"strings"

	cfg "github.com/csee-pm/etl/shared/config"
	"github.com/csee-pm/etl/shared/logger"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/spf13/viper"
)

var UsePstMtdFromFile = ""
var UsePstFmFromFile = ""
var UsePstKabuFromFile = ""
var UseSalmoboFromFile = ""
var UseMtdDistribFromFile = ""
var UseFmDistribFromFile = ""
var UseSDPFromFile = ""
var UseDSEFromFile = ""
var UseGaM2sFromFile = ""
var UseNoMailer = false
var NotInteractive = false

//var SkipPST = false
//var SkipDistrib = false

type Config struct {
	Tunnel               *cfg.TunnelConfig  `yaml:"tunnel"`
	GP                   cfg.GPConfig       `yaml:"gpfat"`
	Impala               cfg.ImpalaConfig   `yaml:"impala"`
	ImpalaLandingSsh     cfg.SSHConfig      `yaml:"impala_landing_ssh"`
	Bigquery             cfg.BigQueryConfig `yaml:"bigquery"`
	EtlConfig            *EtlConfig         `yaml:"etl_config"`
	Email                *cfg.EmailConfig   `yaml:"email_server"`
	ErrorNotifRecipients []string           `yaml:"error_notif_recipients"`
	LogLevel             logger.Level       `yaml:"log_level"`
	WorkDir              string             `yaml:"work_dir"`
	ReportRecipients     []string           `yaml:"report_recipients"`
}

func (c Config) ToMap() map[string]interface{} {
	var cfMap = make(map[string]interface{})
	if c.Tunnel != nil {
		cfMap["tunnel"] = c.Tunnel.ToMap()
	}

	cfMap["gpfat"] = c.GP.ToMap()
	cfMap["impala"] = c.Impala.ToMap()
	cfMap["impala_landing_ssh"] = c.ImpalaLandingSsh.ToMap()

	if c.EtlConfig != nil {
		cfMap["etl_config"] = c.EtlConfig.ToMap()
	}

	cfMap["log_level"] = c.LogLevel
	cfMap["work_dir"] = c.WorkDir
	cfMap["report_submission"] = c.ReportRecipients
	cfMap["error_notif_recipients"] = c.ErrorNotifRecipients

	return cfMap
}

type EtlConfig struct {
	MtdDate *int `yaml:"mtd_date"`
}

func (et EtlConfig) ToMap() map[string]interface{} {
	var etMap = make(map[string]interface{})
	if et.MtdDate != nil {
		etMap["mtd_date"] = *et.MtdDate
	}
	return etMap
}

type NotifierConfig struct {
	Email *cfg.EmailConfig `yaml:"email"`
}

func (n NotifierConfig) ToMap() map[string]interface{} {
	var nMap = make(map[string]interface{})
	if n.Email != nil {
		nMap["email"] = n.Email.ToMap()
	}

	return nMap
}

type DistributionConfig struct {
	Email *cfg.EmailConfig `yaml:"email"`
}

func (d DistributionConfig) ToMap() map[string]interface{} {
	var dMap = make(map[string]interface{})
	if d.Email != nil {
		dMap["email"] = d.Email.ToMap()
	}

	return dMap
}

func GetEtlConfig(v *viper.Viper, key string) (*EtlConfig, error) {
	var cf EtlConfig
	err := v.UnmarshalKey(key, &cf)
	return &cf, err
}

func SetNewConfig(v *viper.Viper) error {
	useTun := utils.StringToBool(cfg.GetPromptValue("Use SSH Tunnel To connect to GP[y/N]: ", "N", cfg.ShouldBeYesOrNo))
	if useTun {
		tunCfg, err := cfg.PromptNewTunnelConfig(v, "tunnel")
		if err != nil {
			return err
		}

		v.Set("tunnel", tunCfg.ToMap())
	}

	fmt.Println("\nPlease provide Greenplum configuration")
	gpCfg, err := cfg.PromptNewGPConfig(v, "gpfat")
	if err != nil {
		return err
	}
	v.Set("gpfat", gpCfg.ToMap())

	fmt.Println("\nPlease provide Impala configuration")
	impalaCfg, err := cfg.PromptNewImpalaConfig(v, "impala")
	if err != nil {
		return err
	}
	v.Set("impala", impalaCfg.ToMap())

	fmt.Println("\nPlease provide SSH configuration for Impala Landing")
	impalaSsh, err := cfg.PromptNewSSHConfig(v, "impala_landing_ssh")
	if err != nil {
		return err
	}
	v.Set("impala_landing_ssh", impalaSsh.ToMap())

	fmt.Println("\nPlease provide Email Server configuration")
	emailCfg, err := cfg.PromptNewEmailServerConfig(v, "email_server")
	if err != nil {
		return err
	}
	v.Set("email_server", emailCfg.ToMap())

	v.Set("report_recipients.default", strings.Split(cfg.GetPromptValue("Default report recipients email address (separate by semicolon \";\" for multiple receivers): ", "", cfg.NoValidation), ";"))
	v.Set("report_recipients.pst", strings.Split(cfg.GetPromptValue("PST report recipients email address (separate by semicolon \";\" for multiple receivers): ", "", cfg.NoValidation), ";"))
	v.Set("report_recipients.distrib", strings.Split(cfg.GetPromptValue("Distrib report recipients email address (separate by semicolon \";\" for multiple receivers): ", "", cfg.NoValidation), ";"))
	v.Set("report_recipients.topkpi", strings.Split(cfg.GetPromptValue("Top KPI report recipients email address (separate by semicolon \";\" for multiple receivers): ", "", cfg.NoValidation), ";"))

	// receivers := utils.StringToStringSlice(cfg.GetPromptValue("PST report recipients email address (separate by semicolon \";\" for multiple receivers): ", "", cfg.NoValidation), ";")
	// v.Set("report_recipients.pst", receivers)

	errNotifRecipients := utils.StringToStringSlice(cfg.GetPromptValue("Error notification recipients email address (separate by semicolon \";\" for multiple receivers): ", "", cfg.NoValidation), ";")
	v.Set("error_notif_recipients", errNotifRecipients)

	return nil
}
