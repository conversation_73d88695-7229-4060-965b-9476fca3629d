package cmd

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"

	apexLog "github.com/apex/log"
	apexcli "github.com/apex/log/handlers/cli"
	"github.com/apex/log/handlers/text"
	etlConfig "github.com/csee-pm/etl/shared/config"
	ctx "github.com/csee-pm/etl/shared/context"
	log "github.com/csee-pm/etl/shared/logger"
	"github.com/csee-pm/etl/shared/notify"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

var configSets []string
var configFile string

type command struct {
	// cmd       *cobra.Command
	short      string
	cmdcontext context.Context
	action     func(*cobra.Command, []string)
	conf       *viper.Viper
	logger     log.Logger
	logBuffer  *bytes.Buffer
}

// type CommandOption func(*cobra.Command)
type CommandOption func(*command)

var logBuffer *bytes.Buffer

func InitCommonRootCommand(name string, newConfigFn func(*viper.Viper) error, options ...CommandOption) *cobra.Command {
	v := viper.New()

	cmd := new(command)

	for _, opt := range options {
		opt(cmd)
	}

	logBuffer = new(bytes.Buffer)
	if cmd.logBuffer != nil {
		logBuffer = cmd.logBuffer
	}

	logWriter := io.MultiWriter(os.Stdout, logBuffer)
	alog := &apexLog.Logger{}
	alog.Handler = apexcli.New(logWriter)
	alog.Handler = text.New(logWriter)

	logger := log.NewApexLogger(alog)

	if cmd.logger != nil {
		logger = cmd.logger
	}

	root := CreateRootCmd(
		name,
		v,
		logger,
		newConfigFn,
		options...,
	)

	return root
}

func CreateRootCmd(name string, v *viper.Viper, logger log.Logger, newConfigFn func(*viper.Viper) error, options ...CommandOption) *cobra.Command {
	// rootcmd := &command{
	// 	cmd: &cobra.Command{
	// 		Use:              name,
	// 		GetReportData:              nil,
	// 		PersistentPreRun: createPreRunAction(newConfigFn),
	// 		Short:            name,
	// 	},
	// 	conf: v,
	// }

	rootcmd := new(command)

	for _, opt := range options {
		opt(rootcmd)
	}

	root := &cobra.Command{
		Use:              name,
		Run:              rootcmd.action,
		Short:            name,
		PersistentPreRun: createPreRunAction(newConfigFn),
	}

	if root.Run == nil {
		root.Run = createDefaultRootAction(name, func(c context.Context) error {
			logger.Error("root action not set")
			return nil
		})
	}

	c := root.Context()
	if c == nil {
		c = context.Background()
	}

	// get executable path
	exePath, err := os.Executable()
	if err != nil {
		logger.Error(err.Error())
		exePath = "./distrib"
	}
	exeDir := filepath.Dir(exePath)

	c = context.WithValue(c, ctx.ContextKeyConfig, v)
	c = context.WithValue(c, ctx.ContextKeyLogger, logger)
	c = context.WithValue(c, ctx.ContextKeyRootDir, exeDir)
	root.SetContext(c)

	cfgFile := filepath.Join(exeDir, "."+name+".yaml")

	v.SetTypeByDefaultValue(true)
	v.SetConfigFile(cfgFile)
	v.SetDefault("log_level", 1)
	v.SetDefault("work_dir", "workdir")
	v.SetDefault("root_dir", exeDir)

	root.AddCommand(NewConfigGetCmd(v))
	root.AddCommand(NewConfigSetCmd(v, newConfigFn))
	root.AddCommand(NewEncryptCmd(v))

	root.PersistentFlags().StringArrayVar(&configSets, "set", nil, "set config")
	root.PersistentFlags().StringVar(&configFile, "config", cfgFile, "config file")

	return root
}

func createDefaultRootAction(processName string, process func(context.Context) error) func(*cobra.Command, []string) {
	return func(cmd *cobra.Command, args []string) {
		// conf, ok := cmd.Context().Value(ctx.ContextKeyConfig).(*viper.Viper)
		// if !ok {
		// 	cmd.PrintErrln("failed to get cmd object from context")
		// }

		logger := ctx.ExtractLogger(cmd.Context())

		// var notifier notify.Notifier

		// if conf.Get("notifier") != nil {
		// 	if conf.Get("notifier.email") != nil {
		// 		notifyConf, err := etlConfig.GetEmailConfig(conf, "notifier.email")
		// 		if err != nil {
		// 			logger.Error(err.Error())
		// 		}

		// 		notifier = notify.NewEmailNotifier(
		// 			notifyConf.Host,
		// 			notifyConf.Port,
		// 			notifyConf.User,
		// 			notifyConf.Pass,
		// 			notifyConf.SenderAddress,
		// 			notifyConf.SenderName,
		// 			notifyConf.Receiver...)
		// 	}
		// }

		// // fmt.Printf("config: %+v\n", cfg.AllSettings())
		// msg := fmt.Sprintf("%s process finished successfully.", processName)
		// subject := fmt.Sprintf("%s Process Success", processName)
		// if err := process(cmd.Context()); err != nil {
		// 	subject = fmt.Sprintf("%s Process Failed", processName)
		// 	msg = fmt.Sprintf("%s process failed. %s", processName, err)
		// 	logger.Error(err.Error())
		// }

		// logger.Info(fmt.Sprintf("%s process finished", processName))
		// msg = fmt.Sprintf("%s\n\n%s", msg, logBuffer.String())
		// if err := sendNotif(notifier, subject, msg); err != nil {
		// 	logger.Error(err.Error())
		// }

		if err := process(cmd.Context()); err != nil {
			logger.Error(err.Error())
		}
	}
}

func sendNotif(nt notify.Notifier, subject string, message string, attachments ...notify.FileAttachment) error {
	if nt != nil {
		return nt.Notify(subject, notify.TextToHTMLString(message), attachments...)
	}

	return nil
}

func WithContext(c context.Context) CommandOption {
	return func(cmd *command) {
		cmd.cmdcontext = c
	}
}

func WithDescription(description string) CommandOption {
	return func(c *command) {
		c.short = description
	}
}

func WithAction(action func(*cobra.Command, []string)) CommandOption {
	return func(c *command) {
		c.action = action
	}
}

func WithRootProcess(processName string, process func(context.Context) error) CommandOption {
	return func(c *command) {
		c.action = createDefaultRootAction(processName, process)
	}
}

func WithViper(v *viper.Viper) CommandOption {
	return func(c *command) {
		c.conf = v
	}
}

// func WithSubCommands(commands ...*cobra.Command) CommandOption {
// 	return func(c *command) {
// 		c.cmd.AddCommand(commands...)
// 	}
// }

// func WithAddFlagSet(flagset *pflag.FlagSet) CommandOption {
// 	return func(c *command) {
// 		c.cmd.Flags().AddFlagSet(flagset)
// 	}
// }

// func WithAddPersistentFlagSet(flagset *pflag.FlagSet) CommandOption {
// 	return func(c *command) {
// 		c.cmd.PersistentFlags().AddFlagSet(flagset)
// 	}
// }

// func WithPreRun(action func(*cobra.Command, []string)) CommandOption {
// 	return func(c *command) {
// 		c.cmd.PreRun = action
// 	}
// }

func WithLogBuffer(buf *bytes.Buffer) CommandOption {
	return func(c *command) {
		c.logBuffer = buf
	}
}

func WithLogger(logger log.Logger) CommandOption {
	return func(c *command) {
		c.logger = logger
	}
}

// func WithPrerunAction(action func(*cobra.Command, []string), persistant bool) CommandOption {
// 	return func(c *cobra.Command) {
// 		if persistant {
// 			c.PersistentPreRun = action
// 			return
// 		}

// 		c.PreRun = action
// 	}
// }

func createInitConfig(v *viper.Viper, newConfigFn func(*viper.Viper) error, logger log.Logger) func() {
	if _, err := os.Stat(configFile); errors.Is(err, os.ErrNotExist) {
		logger.Info("config not found")
		logger.Info("creating new config")
		fmt.Println("Please input your configuration")

		notInteractive := v.GetBool("not-interactive")
		if notInteractive {
			logger.Error("config not found")
			logger.Error("please run config-set command to create new config")
			os.Exit(1)
		}

		err := newConfigFn(v)
		if err != nil {
			logger.Error(err.Error())
			os.Exit(1)
		}

		if err := v.WriteConfig(); err != nil {
			logger.Error(err.Error())
			os.Exit(1)
		}
	}

	return func() {
		// v.SetConfigFile(configFile)
		v.AutomaticEnv() // read in environment variables that match

		// If a config file is found, read it in.
		if err := v.ReadInConfig(); err == nil {
			logger.Debug(fmt.Sprintf("Using config file: %s", v.ConfigFileUsed()))
		} else {
			// logger.Error(fmt.Sprintf("failed to read configFile. %s", err))
			cobra.CheckErr(err)
		}

		if len(configSets) > 0 {
			ovrMap := etlConfig.ArraySetsToMap(configSets)
			if err := etlConfig.Override(v, ovrMap); err != nil {
				logger.Error(err.Error())
			}
		}
	}
}

func createInitLogger(v *viper.Viper, logger log.Logger) func() {
	return func() {
		level := v.GetString("log_level")
		var logLevel log.Level = log.InfoLevel
		switch level {
		case "0", "debug":
			logLevel = log.DebugLevel
		case "1", "info":
			logLevel = log.InfoLevel
		case "2", "warn":
			logLevel = log.WarnLevel
		case "3", "error":
			logLevel = log.ErrorLevel
		}

		logger.SetLevel(logLevel)
	}
}

func createPreRunAction(newConfigFn func(*viper.Viper) error) func(*cobra.Command, []string) {
	return func(cmd *cobra.Command, args []string) {
		cfg, ok := cmd.Context().Value(ctx.ContextKeyConfig).(*viper.Viper)
		if !ok {
			cmd.PrintErrln("failed to get cmd object from context")
		}
		logger := ctx.ExtractLogger(cmd.Context())

		if cmd.Name() != "config-set" || len(args) > 0 {
			createInitConfig(cfg, newConfigFn, logger)()
		}

		createInitLogger(cfg, logger)()
	}
}
