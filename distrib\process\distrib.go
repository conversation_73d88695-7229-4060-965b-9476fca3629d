package process

import (
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	cfg "github.com/csee-pm/etl/distrib/config"
	"github.com/csee-pm/etl/shared/channel"
	ctx "github.com/csee-pm/etl/shared/context"
	etlDb "github.com/csee-pm/etl/shared/db"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/csee-pm/etl/shared/utils/xlutil"
	"github.com/xuri/excelize/v2"
	"gopkg.in/guregu/null.v4"
)

type distribProcess struct{}

func NewDistribProcess() distribProcess {
	return distribProcess{}
}

func (dst distribProcess) RunDistrib(c context.Context) (*DistribReport, error) {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	logger.Debug("starting Distrib ETL")
	workDate := time.Now().AddDate(0, 0, -3)

	var err error
	if conf.Get("etl.mtd_date") != nil {
		mtdDate := conf.GetString("etl.mtd_date")
		workDate, err = time.Parse("20060102", mtdDate)
		if err != nil {
			return nil, fmt.Errorf("failed to parse mtd_date. %s", err)
		}
	}

	conf.Set("work_date", workDate)

	fromFile := cfg.UseDistribFromFile

	var data []DistribData
	if fromFile != "" {
		data, err = dst.getDataFromFile(fromFile)
	} else {
		data, err = dst.getData(c, workDate)
	}

	if err != nil {
		return nil, err
	}

	if fromFile == "" {
		if err := dst.writeRawData(c, data); err != nil {
			return nil, err
		}
	}

	reportData, err := dst.postProcessData(data)
	if err != nil {
		return nil, err
	}

	target, err := dst.getBrandSndTarget(c)
	if err != nil {
		return nil, err
	}

	reportData.Target = target

	return &reportData, nil
}

func (dst distribProcess) getDataFromFile(fpath string) ([]DistribData, error) {
	f, err := os.Open(fpath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	var data []DistribData
	// if err := utils.ReadFromCsv(f, &data, utils.WithHeaderLine(0)); err != nil {
	// 	return nil, err
	// }

	cr := csv.NewReader(f)

	cr.Read() // skip header
	for {
		record, err := cr.Read()

		if errors.Is(err, io.EOF) {
			break
		}

		if err != nil {
			return nil, err
		}

		mtd, err := strconv.ParseFloat(record[4], 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse MTD: %s. %s", record[4], err)
		}

		lmtd, err := strconv.ParseFloat(record[3], 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse LMTD: %s. %s", record[3], err)
		}

		data = append(data, DistribData{
			Brand:  record[0],
			Circle: null.StringFrom(record[1]),
			Region: null.StringFrom(record[2]),
			DistribMtdParam: DistribMtdParam{
				MTD:       mtd,
				LMTD:      lmtd,
				DtID:      record[5],
				Parameter: record[6],
			},
		})
	}

	return data, nil
}

func (dst distribProcess) getData(c context.Context, workDate time.Time) ([]DistribData, error) {
	logger := ctx.ExtractLogger(c)
	logger.Debug("querying data for Distrib")

	var wg sync.WaitGroup
	cCancel, cancel := context.WithCancel(c)
	defer cancel()

	var impalaData []DistribData
	var gpfatData []DistribData

	mtdDate, err := strconv.Atoi(workDate.Format("20060102"))
	if err != nil {
		return nil, err
	}

	wg.Add(1)
	impalaResult := channel.RunAsyncContext(cCancel, func() ([]DistribData, error) {
		return dst.getImpalaData(cCancel, mtdDate)
	})

	go func() {
		defer wg.Done()
		for res := range impalaResult {
			res.Map(func(data []DistribData) {
				impalaData = data
			}).MapErr(func(er error) {
				logger.Error("failed to get Impala data", "error", er)
				cancel()
			})
		}
	}()

	wg.Add(1)
	gpfatResult := channel.RunAsyncContext(cCancel, func() ([]DistribData, error) {
		return dst.get3idData(cCancel, mtdDate)
	})

	go func() {
		defer wg.Done()
		for res := range gpfatResult {
			res.Map(func(data []DistribData) {
				gpfatData = data
			}).MapErr(func(er error) {
				logger.Error("failed to get GP data", "error", er)
				cancel()
			})
		}
	}()

	wg.Wait()

	return append(impalaData, gpfatData...), nil
}

func (dst distribProcess) getImpalaData(c context.Context, mtdDate int) ([]DistribData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := procFS.ReadFile("files/im3_dist_mtd.sql")
	if err != nil {
		return nil, err
	}
	logger.Debug("querying data for IM3 Distrib")

	params := map[string]*etlDb.ParamValue{
		"dt_id": {Name: "dt_id", Value: strconv.Itoa(mtdDate)},
	}

	return etlProc.QueryImpalaData[DistribData](c, string(buf), params)
}

func (dst distribProcess) get3idData(c context.Context, mtdDate int) ([]DistribData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := procFS.ReadFile("files/3id_dist_mtd.sql")
	if err != nil {
		return nil, err
	}
	logger.Debug("querying data for 3ID Distrib")

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_int": {Name: "mtd_dt_int", Value: mtdDate},
	}

	return etlProc.QueryGreenplumData[DistribData](c, string(buf), params)
}

func (dst distribProcess) writeRawData(c context.Context, data []DistribData) error {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)
	workDir := conf.GetString("work_dir")
	csvFilePath := fmt.Sprintf("%s/distrib_%s.csv", workDir, time.Now().Format("20060102150405"))

	logger.Debug("writing raw data", "path", csvFilePath)
	if err := utils.WriteToCsv(csvFilePath, data); err != nil {
		logger.Error("failed to write CSV file", "path", csvFilePath, "error", err)
	}

	logger.Info("CSV file written", "path", csvFilePath)
	return nil
}

func (dst distribProcess) postProcessData(data []DistribData) (DistribReport, error) {
	brandData := map[string]struct {
		regionMap        map[string]map[string]*DistribData
		circleMap        map[string]map[string]*DistribData
		nationalParamMap map[string]*DistribData
		reportEntries    []DistribReportData
	}{
		"IOH": {
			regionMap:        make(map[string]map[string]*DistribData),
			circleMap:        make(map[string]map[string]*DistribData),
			nationalParamMap: make(map[string]*DistribData),
		},
		"IM3": {
			regionMap:        make(map[string]map[string]*DistribData),
			circleMap:        make(map[string]map[string]*DistribData),
			nationalParamMap: make(map[string]*DistribData),
		},
		"3ID": {
			regionMap:        make(map[string]map[string]*DistribData),
			circleMap:        make(map[string]map[string]*DistribData),
			nationalParamMap: make(map[string]*DistribData),
		},
	}

	iohBrandInfo := brandData["IOH"]

	for i, d := range data {
		brand := d.Brand
		brandInfo, exists := brandData[brand]
		if !exists {
			continue
		}

		circle := d.Circle.String
		region := d.Region.String
		param := d.Parameter

		paramData := data[i]
		if paramData.Parameter == "Secondary" {
			paramData.MTD = paramData.MTD / 1000_000
			paramData.LMTD = paramData.LMTD / 1000_000
		}

		// process region data
		if _, ok := brandInfo.regionMap[region]; !ok {
			brandInfo.regionMap[region] = make(map[string]*DistribData)
		}
		brandInfo.regionMap[region][param] = utils.PtrToValCopy(paramData)

		if _, ok := iohBrandInfo.regionMap[region]; !ok {
			iohBrandInfo.regionMap[region] = make(map[string]*DistribData)
		}

		iohRegionParams, exists := iohBrandInfo.regionMap[region][param]
		if !exists {
			iohBrandInfo.regionMap[region][param] = &DistribData{
				Brand:           "IOH",
				Circle:          paramData.Circle,
				Region:          paramData.Region,
				DistribMtdParam: paramData.DistribMtdParam,
			}
		} else {
			iohRegionParams.MTD += paramData.MTD
			iohRegionParams.LMTD += paramData.LMTD
		}

		// aggregate circle data
		if _, ok := brandInfo.circleMap[circle]; !ok {
			brandInfo.circleMap[circle] = make(map[string]*DistribData)
		}

		circleParams, exists := brandInfo.circleMap[circle][param]
		if !exists {
			brandInfo.circleMap[circle][param] = utils.PtrToValCopy(paramData)
		} else {
			circleParams.MTD += paramData.MTD
			circleParams.LMTD += paramData.LMTD
		}

		if _, ok := iohBrandInfo.circleMap[circle]; !ok {
			iohBrandInfo.circleMap[circle] = make(map[string]*DistribData)
		}

		iohCircleParams, exists := iohBrandInfo.circleMap[circle][param]
		if !exists {
			iohBrandInfo.circleMap[circle][param] = &DistribData{
				Brand:           "IOH",
				Circle:          paramData.Circle,
				Region:          paramData.Region,
				DistribMtdParam: paramData.DistribMtdParam,
			}
		} else {
			iohCircleParams.MTD += paramData.MTD
			iohCircleParams.LMTD += paramData.LMTD
		}

		// aggregate national data
		nationalParams, exists := brandInfo.nationalParamMap[param]
		if !exists {
			brandInfo.nationalParamMap[param] = utils.PtrToValCopy(paramData)
		} else {
			nationalParams.MTD += paramData.MTD
			nationalParams.LMTD += paramData.LMTD
		}

		iohNationalParams, exists := iohBrandInfo.nationalParamMap[param]
		if !exists {
			iohBrandInfo.nationalParamMap[param] = &DistribData{
				Brand:           "IOH",
				Circle:          paramData.Circle,
				Region:          paramData.Region,
				DistribMtdParam: paramData.DistribMtdParam,
			}
		} else {
			iohNationalParams.MTD += paramData.MTD
			iohNationalParams.LMTD += paramData.LMTD
		}
	}

	// Build report entries
	for brand, brandInfo := range brandData {
		// Add region entries
		for region, regionParams := range brandInfo.regionMap {
			brandInfo.reportEntries = append(brandInfo.reportEntries, DistribReportData{
				EntityType:   "REGION",
				EntityName:   region,
				ParamDataMap: utils.MapToMap(regionParams, func(k string, v *DistribData) (string, DistribData) { return k, *v }),
			})
		}

		// Add circle entries
		for circle, circleParams := range brandInfo.circleMap {
			brandInfo.reportEntries = append(brandInfo.reportEntries, DistribReportData{
				EntityType:   "CIRCLE",
				EntityName:   circle,
				ParamDataMap: utils.MapToMap(circleParams, func(k string, v *DistribData) (string, DistribData) { return k, *v }),
			})
		}

		// Add national entries
		brandInfo.reportEntries = append(brandInfo.reportEntries, DistribReportData{
			EntityType:   "NATIONAL",
			EntityName:   "NATIONAL",
			ParamDataMap: utils.MapToMap(brandInfo.nationalParamMap, func(k string, v *DistribData) (string, DistribData) { return k, *v }),
		})

		brandData[brand] = brandInfo
	}

	return DistribReport{
		IOH: brandData["IOH"].reportEntries,
		IM3: brandData["IM3"].reportEntries,
		Tri: brandData["3ID"].reportEntries,
	}, nil
}

func (dst distribProcess) getSndTarget(c context.Context) ([]SndTarget, error) {
	logger := ctx.ExtractLogger(c)
	exepath := ctx.ExtractRootDir(c)

	sndFPath := filepath.Join(exepath, "snd_target.csv")

	logger.Debug("reading snd target data", "path", sndFPath)

	// check if file exists
	if !utils.FileExists(sndFPath) {
		return nil, fmt.Errorf("file not found: %s", sndFPath)
	}

	f, err := os.Open(sndFPath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	var sndTargets []SndTarget
	cr := csv.NewReader(f)

	cr.Read() // skip header
	for {
		record, err := cr.Read()

		if errors.Is(err, io.EOF) {
			break
		}

		if err != nil {
			return nil, err
		}

		targetGA, err := strconv.Atoi(record[3])
		if err != nil {
			logger.Error("failed to parse target GA", "error", err)
			continue
		}

		targetSecMn, err := strconv.Atoi(record[4])
		if err != nil {
			logger.Error("failed to parse target SecMn", "error", err)
			continue
		}

		sndTargets = append(sndTargets, SndTarget{
			Circle:    record[0],
			Region:    record[1],
			Brand:     record[2],
			TargetKpi: TargetKpi{TargetGA: targetGA, TargetSecMn: targetSecMn},
		})

	}

	return sndTargets, nil
}

func (dst distribProcess) getBrandSndTarget(c context.Context) (BrandSndTarget, error) {
	brandData := map[string]struct {
		RegionMap      map[string]*TargetKpi
		CircleMap      map[string]*TargetKpi
		NationalTarget *TargetKpi
		Entries        []EntityTarget
	}{
		"IOH": {
			RegionMap:      make(map[string]*TargetKpi),
			CircleMap:      make(map[string]*TargetKpi),
			NationalTarget: &TargetKpi{},
		},
		"IM3": {
			RegionMap:      make(map[string]*TargetKpi),
			CircleMap:      make(map[string]*TargetKpi),
			NationalTarget: &TargetKpi{},
		},
		"3ID": {
			RegionMap:      make(map[string]*TargetKpi),
			CircleMap:      make(map[string]*TargetKpi),
			NationalTarget: &TargetKpi{},
		},
	}

	sndTargets, err := dst.getSndTarget(c)
	iohBrandInfo := brandData["IOH"]

	if err != nil {
		return BrandSndTarget{}, err
	}

	for _, t := range sndTargets {
		circle := t.Circle
		region := t.Region
		brand := t.Brand

		target := t.TargetKpi

		brandInfo, exists := brandData[brand]
		if !exists {
			continue
		}

		// process region data
		brandInfo.RegionMap[region] = utils.PtrToValCopy(target)

		if _, ok := iohBrandInfo.RegionMap[region]; !ok {
			iohBrandInfo.RegionMap[region] = &TargetKpi{}
		}

		iohBrandInfo.RegionMap[region].TargetGA += target.TargetGA
		iohBrandInfo.RegionMap[region].TargetSecMn += target.TargetSecMn

		// aggregate circle data
		circleTarget := brandInfo.CircleMap[circle]
		if circleTarget == nil {
			brandInfo.CircleMap[circle] = utils.PtrToValCopy(target)
		} else {
			circleTarget.TargetGA += target.TargetGA
			circleTarget.TargetSecMn += target.TargetSecMn
		}

		if _, ok := iohBrandInfo.CircleMap[circle]; !ok {
			iohBrandInfo.CircleMap[circle] = &TargetKpi{}
		}

		iohBrandInfo.CircleMap[circle].TargetGA += target.TargetGA
		iohBrandInfo.CircleMap[circle].TargetSecMn += target.TargetSecMn

		// aggregate national data
		brandInfo.NationalTarget.TargetGA += target.TargetGA
		brandInfo.NationalTarget.TargetSecMn += target.TargetSecMn

		iohBrandInfo.NationalTarget.TargetGA += target.TargetGA
		iohBrandInfo.NationalTarget.TargetSecMn += target.TargetSecMn
	}

	// Build report entries
	for brand, brandInfo := range brandData {
		// Add region entries
		for region, target := range brandInfo.RegionMap {
			brandInfo.Entries = append(brandInfo.Entries, EntityTarget{
				EntityType: "REGION",
				EntityName: region,
				Target:     *target,
			})
		}

		// Add circle entries
		for circle, target := range brandInfo.CircleMap {
			brandInfo.Entries = append(brandInfo.Entries, EntityTarget{
				EntityType: "CIRCLE",
				EntityName: circle,
				Target:     *target,
			})
		}

		// Add national entries
		brandInfo.Entries = append(brandInfo.Entries, EntityTarget{
			EntityType: "NATIONAL",
			EntityName: "NATIONAL",
			Target:     *brandInfo.NationalTarget,
		})

		brandData[brand] = brandInfo
	}

	return BrandSndTarget{
		IOH: brandData["IOH"].Entries,
		IM3: brandData["IM3"].Entries,
		Tri: brandData["3ID"].Entries,
	}, nil
}

func (dst distribProcess) WriteReport(c context.Context, xl *excelize.File, report *DistribReport) error {
	logger := ctx.ExtractLogger(c)
	conf := ctx.ExtractConfig(c)

	logger.Debug("writing Distrib IM3")
	if err := dst.writeReport(xl, report.IM3, report.Target.IM3, 38); err != nil {
		return err
	}

	logger.Debug("writing Distrib 3ID")
	if err := dst.writeReport(xl, report.Tri, report.Target.Tri, 65); err != nil {
		return err
	}

	logger.Debug("writing Distrib IOH")
	if err := dst.writeReport(xl, report.IOH, report.Target.IOH, 10); err != nil {
		return err
	}

	asofDate := conf.GetTime("work_date")
	asofDateStr := asofDate.Format("02-Jan-2006")

	shname := "LMTD VS MTD"
	xl.SetCellValue(shname, "C2", "data as of "+asofDateStr)

	return nil
}

func (dst distribProcess) writeReport(xl *excelize.File, data []DistribReportData, target []EntityTarget, startRow int) error {
	shname := "LMTD VS MTD"

	regionData := utils.SliceFilter(data, func(d DistribReportData) bool { return d.EntityType == "REGION" })
	circleData := utils.SliceFilter(data, func(d DistribReportData) bool { return d.EntityType == "CIRCLE" })
	nationalData := utils.SliceFilter(data, func(d DistribReportData) bool { return d.EntityType == "NATIONAL" })

	regionTarget := utils.SliceFilter(target, func(d EntityTarget) bool { return d.EntityType == "REGION" })
	circleTarget := utils.SliceFilter(target, func(d EntityTarget) bool { return d.EntityType == "CIRCLE" })
	nationalTarget := utils.SliceFilter(target, func(d EntityTarget) bool { return d.EntityType == "NATIONAL" })

	regionMap := utils.SliceToMap(regionData, func(d DistribReportData) (string, map[string]DistribData) {
		return d.EntityName, d.ParamDataMap
	})
	circleMap := utils.SliceToMap(circleData, func(d DistribReportData) (string, map[string]DistribData) {
		return d.EntityName, d.ParamDataMap
	})
	nationalMap := utils.SliceToMap(nationalData, func(d DistribReportData) (string, map[string]DistribData) {
		return d.EntityName, d.ParamDataMap
	})

	regionTargetMap := utils.SliceToMap(regionTarget, func(d EntityTarget) (string, TargetKpi) {
		return d.EntityName, d.Target
	})
	circleTargetMap := utils.SliceToMap(circleTarget, func(d EntityTarget) (string, TargetKpi) {
		return d.EntityName, d.Target
	})
	nationalTargetMap := utils.SliceToMap(nationalTarget, func(d EntityTarget) (string, TargetKpi) {
		return d.EntityName, d.Target
	})

	getDistribData := func(entityName string) map[string]DistribData {
		if entityName == "INDONESIA" {
			return nationalMap["NATIONAL"]
		}

		if entityName == "JAYA" {
			entityName = "JAKARTA RAYA"
		}

		distrib, exists := regionMap[entityName]
		if !exists {
			distrib = circleMap[entityName]
		}

		return distrib
	}

	getTargetData := func(entityName string) TargetKpi {
		if entityName == "INDONESIA" {
			return nationalTargetMap["NATIONAL"]
		}

		if entityName == "JAYA" {
			entityName = "JAKARTA RAYA"
		}

		target, exists := regionTargetMap[entityName]
		if !exists {
			target = circleTargetMap[entityName]
		}

		return target
	}

	for i := 0; i < 18; i++ {
		row := startRow + i
		entityName, err := xl.GetCellValue(shname, xlutil.Cell(row, 3).Address())
		if err != nil {
			return err
		}

		entityName = strings.TrimSpace(entityName)

		distrib := getDistribData(entityName)
		if distrib == nil {
			continue
		}

		target := getTargetData(entityName)

		for k, v := range distrib {
			col, ok := distribColMap[k]
			if !ok {
				continue
			}

			xl.SetCellValue(shname, xlutil.Cell(row, col).Address(), v.MTD)
			if !utils.SliceContains([]string{"Secondary", "RGU GA", "DSE", "Addressable Site", "SDP/3KIOSK"}, k) {
				xl.SetCellValue(shname, xlutil.Cell(row, col-1).Address(), v.LMTD)
			}
		}

		xl.SetCellValue(shname, xlutil.Cell(row, distribColMap["Target GA"]).Address(), target.TargetGA/1000)
		xl.SetCellValue(shname, xlutil.Cell(row, distribColMap["Target SecMn"]).Address(), target.TargetSecMn)
	}

	return nil
}

var distribColMap = map[string]int{
	"Site 3-QSSO":            6,
	"Site 5-QURO":            8,
	"Site w/ <1 GAD/Day":     10,
	"DSE w/ <12 GAD":         13,
	"DSE w/ <5 Mn Secondary": 15,
	"SDP <350 GA":            20,
	"SDP <75 Mn":             22,
	"Secondary":              25,
	"RGU GA":                 27,
	"DSE":                    40,
	"Addressable Site":       41,
	"SDP/3KIOSK":             42,
	"Target GA":              33,
	"Target SecMn":           32,
}
