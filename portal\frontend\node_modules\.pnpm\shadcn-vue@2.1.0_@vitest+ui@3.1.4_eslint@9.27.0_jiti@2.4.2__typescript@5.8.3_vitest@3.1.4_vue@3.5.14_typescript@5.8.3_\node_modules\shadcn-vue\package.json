{"name": "shadcn-vue", "type": "module", "version": "2.1.0", "description": "Add components to your apps.", "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/unovue/shadcn-vue.git", "directory": "packages/cli"}, "keywords": ["components", "ui", "vue", "nuxt", "tailwind", "radix-ui", "radix-vue", "reka-ui", "shadcn", "shadcn-vue"], "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./registry": {"types": "./dist/registry/index.d.ts", "default": "./dist/registry/index.js"}}, "bin": "./dist/index.js", "files": ["dist"], "peerDependencies": {"@vitest/ui": "*", "vitest": "*"}, "dependencies": {"@unovue/detypes": "^0.8.5", "@vue/compiler-sfc": "^3.5", "commander": "^12.1.0", "consola": "^3.4.0", "cosmiconfig": "^9.0.0", "deepmerge": "^4.3.1", "diff": "^7.0.0", "fs-extra": "^11.3.0", "get-tsconfig": "^4.10.0", "lodash-es": "^4.17.21", "magic-string": "^0.30.17", "nypm": "^0.5.2", "ofetch": "^1.4.1", "ora": "^8.2.0", "pathe": "^2.0.3", "pkg-types": "^1.3.1", "postcss": "^8.5.2", "prompts": "^2.4.2", "reka-ui": "^2.0.0", "stringify-object": "^5.0.0", "tailwindcss": "^3.4.16", "tinyexec": "^0.3.2", "tinyglobby": "^0.2.10", "ts-morph": "^24.0.0", "undici": "^7.3.0", "vue-metamorph": "3.2.0", "zod": "^3.24.2"}, "devDependencies": {"@types/diff": "^7.0.1", "@types/fs-extra": "^11.0.4", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.4", "@types/prompts": "^2.4.9", "@types/stringify-object": "^4.0.5", "msw": "^2.7.3", "tsup": "^8.3.6", "type-fest": "^4.34.1", "typescript": "^5.7.3"}, "scripts": {"dev": "tsup --watch", "build": "tsup", "typecheck": "tsc --noEmit", "clean": "node ./scripts/rimraf.js", "lint": "eslint .", "lint:fix": "eslint --fix .", "start:dev": "REGISTRY_URL=http://localhost:5173/r node dist/index.js", "start": "node dist/index.js", "release": "changeset version", "pub:beta": "pnpm build && pnpm publish --no-git-checks --access public --tag beta", "pub:next": "pnpm build && pnpm publish --no-git-checks --access public --tag next", "pub:release": "pnpm build && pnpm publish  --no-git-checks --access public", "test": "vitest run", "test:update": "vitest run -u", "test:ui": "vitest --ui"}}