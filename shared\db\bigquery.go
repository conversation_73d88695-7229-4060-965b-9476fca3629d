package db

import (
	"context"

	"cloud.google.com/go/auth"
	"cloud.google.com/go/bigquery"
)

type BigQuery struct {
	Client *bigquery.Client
}

type bqOptions struct {
	projectID string
	creds     *auth.Credentials
}

type BigQueryOption func(o *bqOptions)

func WithCredentials(creds *auth.Credentials) BigQueryOption {
	return func(o *bqOptions) {
		o.creds = creds
	}
}

func NewBigQuery(projectID string, creds *auth.Credentials) (*BigQuery, error) {
	client, err := bigquery.NewClient(context.Background(), projectID)
	if err != nil {
		return nil, err
	}

	return &BigQuery{Client: client}, nil
}

func (bq *BigQuery) Close() error {
	return bq.Client.Close()
}
