with kpi_hg as (
    select
        to_char(a.dt_id, 'YYYYMMDD')::int dt,
        r.circle,
        r.region_circle,
        r.kabkot_nm,
        sum(case when a.kpi_id = 'SUB0017' then a.metric else null end) rgu90d,
        sum(case when a.kpi_id = 'SUB0061' then a.metric else null end) rgu90d_hvc,
        sum(case when a.kpi_id = 'SUB0008' then a.metric else null end) rgu90d_gross_churn,
        sum(case when a.kpi_id = 'CRN0005' then a.metric else null end) rgu90d_hvc_gross_churn,
        sum(case when a.kpi_id = 'SUB0011' then a.metric else null end) rgu90d_churn_back,
        sum(case when a.kpi_id = 'CRN0009' then a.metric else null end) rgu90d_hvc_churn_back
    from
        mis.hg_kpi_site_wise_dly a
        left join
        ioh_biadm.ref_site_h3i r
        on
            a.site_id = r.site_id
    where
        a.kpi_id in ('SUB0061', 'SUB0017', 'SUB0008', 'CRN0005', 'SUB0011', 'CRN0009')
    and a.dt_id >= date_trunc('month', to_date(${mtd_dt_int}::varchar, 'YYYYMMDD') + interval '-1 month')
    and a.dt_id <= to_date(${mtd_dt_int}::text, 'YYYYMMDD')
    group by 1, 2, 3, 4
),
kpi_mis as (
    select
        load_dt_sk_id dt,
        r.circle,
        r.region_circle,
        r.kabkot_nm,
        round((coalesce(sum(case when kpi_code = 'rev_mobo' then value else null end), sum(case when kpi_code = 'rev_mobo_ori' then value else null end)) +
            coalesce(sum(case when kpi_code = 'rev_nondata' then value else null end), sum(case when kpi_code = 'rev_nondata_ori' then value else null end)) +
            coalesce(sum(case when kpi_code = 'rev_organic' then value else null end), sum(case when kpi_code = 'rev_organic_ori' then value else null end))) * 1.11, 3) total_rev_gross,
        sum(case when a.kpi_code = 'rgu90_gross_add' then value else null end) rgu90_ga,
        sum(case when kpi_code = 'vlr_daily' then value else null end) vlr_subs,
        sum(case when kpi_code = 'rgu30_closing_base' then value else null end) rgu30d,
        sum(case when kpi_code = 'rgu90_closing_base' then value else null end) rgu90d,
        sum(case when kpi_code = 'rgu90_gross_churn' then value else null end) rgu90d_gross_churn,
        sum(case when kpi_code = 'rgu90_churn_back' then value else null end) rgu90d_churn_back,
        sum(case when kpi_code = 'sso_any' then value else null end) sso,
        sum(case when kpi_code = 'qsso_any' then value else null end) qsso,
        sum(case when kpi_code = 'uro_any' then value else null end) uro,
        sum(case when kpi_code = 'quro_any' then value else null end) quro,
        round(sum(case when kpi_code = 'voice traffic' then value else null end), 3) voice_traffic_min,
        round(sum(case when kpi_code = 'data traffic' then value/1024 else null end), 3) data_traffic_gb
    from
        mis.project_ioh_kpi_daily_tracker_site a
        left join
        ioh_biadm.ref_site_h3i r
        on
            r.site_id = case when length(a.site_id) <= 5 then lpad(a.site_id, 6, '0') else a.site_id end
    where
        a.load_dt_sk_id >= to_char(date_trunc('month', to_date(${mtd_dt_int}::varchar, 'YYYYMMDD') + interval '-1 month'), 'YYYYMMDD')::int
    and a.load_dt_sk_id <= ${mtd_dt_int}
    group by 1, 2, 3, 4
),
kpi_dly as (
    select
        (a.dt / 100)::varchar month_id,
        a.dt::varchar         dt_id,
        cast(substr(a.dt::varchar, 7, 2) as int) day_int,
        a.circle,
        a.region_circle,
        a.kabkot_nm,
        a.total_rev_gross,
        a.rgu90_ga            rgu_ga,
        a.vlr_subs,
        a.rgu30d,
        d.rgu90d,
        d.rgu90d_hvc,
        d.rgu90d_gross_churn,
        d.rgu90d_hvc_gross_churn,
        d.rgu90d_churn_back,
        d.rgu90d_hvc_churn_back,
        a.sso,
        a.qsso,
        a.uro,
        a.quro,
        a.voice_traffic_min,
        a.data_traffic_gb
    from
        kpi_mis a
        left join
        kpi_hg d
        on
            a.dt = d.dt
        and a.circle = d.circle
        and a.region_circle = d.region_circle
        and a.kabkot_nm = d.kabkot_nm
),
max_date as (
    select
        substr(dt_id, 1, 6) month_id,
        max(dt_id) last_date
    from
        kpi_dly
    where
        day_int <= cast(substr(${mtd_dt_int}::varchar, 7, 2) as int)
    group by 1
),
kpi_sum as (
    select
        a.month_id,
        a.circle,
        a.region_circle,
        a.kabkot_nm,
        max(a.dt_id) asof_date,
        sum(a.total_rev_gross) total_rev_gross,
        sum(a.rgu_ga) rgu_ga,
        sum(a.data_traffic_gb) data_traffic_gb
    from
        kpi_dly a
    where
        a.day_int <= cast(substr(${mtd_dt_int}::varchar, 7, 2) as int)
    group by 1, 2, 3, 4
),
kpi_snap as (
    select
        a.month_id,
        a.circle,
        a.region_circle,
        a.kabkot_nm,
        a.vlr_subs,
        a.rgu30d,
        a.rgu90d,
        a.rgu90d_hvc,
        a.rgu90d_gross_churn,
        a.rgu90d_hvc_gross_churn,
        a.rgu90d_churn_back,
        a.rgu90d_hvc_churn_back,
        a.sso,
        a.qsso,
        a.uro,
        a.quro
    from
        kpi_dly a
        inner join
        max_date b
        on
            a.dt_id = b.last_date
),
subs_site as (
    select
        a.sbscrptn_ek_id,
        a.site_id
    from
        (
            select distinct
                sbscrptn_ek_id,
                case
                    when length(trim(site_id_90)) < 6 then lpad(trim(site_id_90), 6, '0')
                    else trim(site_id_90) end                                                site_id,
                row_number() over (partition by sbscrptn_ek_id order by dt desc, site_id_90) rnk
            from
                mis.ioh_subscriber_site_attribs_rolling a
            where
                  dt in (to_char(to_date(${mtd_dt_int}::varchar, 'YYYYMMDD') + interval '-1 month', 'YYYYMMDD')::int, ${mtd_dt_int})
        ) a
    where
        a.rnk = 1
),
packsub as (
    select
        (a.periode_data/100)::varchar month_id,
        r.circle,
        r.region_circle,
        r.kabkot_nm,
        count(distinct a.sbscrptn_ek_id) subs_90d,
        sum(case when a.rgu30 = 1 then 1 else 0 end) subs_30d,
        sum(case when a.flag_pack_mtd in ('DATA PACK', 'VS PACK') then 1 else 0 end) subs_vsdpack,
        sum(case when a.flag_pack_mtd = 'DATA PACK' then 1 else 0 end) subs_datapack
    from
        analytics.mkt_rgu90d_base_and_pack a
        left join
        subs_site b
        on
            a.sbscrptn_ek_id = b.sbscrptn_ek_id
        left join
        ioh_biadm.ref_site_h3i r
        on
            b.site_id = r.site_id
    where
        periode_data in (to_char(to_date(${mtd_dt_int}::varchar, 'YYYYMMDD') + interval '-1 month', 'YYYYMMDD')::int, ${mtd_dt_int})
    group by 1, 2, 3, 4
),
dse as (
    select
        mth_id month_id,
        circle,
        replace(region,'_',' ') region,
        kabupaten kabkot_nm,
        count(distinct se_partnerid) dse_cnt
    from
        marketing.snd_outlet_mapping
    where
        mth_id in (to_char(to_date(${mtd_dt_int}::varchar, 'YYYYMMDD') + interval '-1 month', 'YYYYMM'), (${mtd_dt_int}/100)::varchar)
    group by
        1, 2, 3, 4
)
select
    a.month_id,
    case when a.month_id = substr(${mtd_dt_int}::varchar, 1, 6) then 'MTD' else 'LMTD' end period,
    m.last_date asof_date,
    a.circle,
    a.region_circle region,
    a.kabkot_nm kabupaten,
    k.flag,
    '3ID' brand,
    round(a.total_rev_gross / 1000000, 3) total_rev_gross_mn,
    round(a.data_traffic_gb, 3) traffic_gb,
    coalesce(b.rgu30d, 0) rgs30d,
    coalesce(b.rgu90d, 0) rgs90d,
    coalesce(a.rgu_ga, 0) rgu_ga,
    coalesce(b.qsso, 0) qsso,
    coalesce(b.quro, 0) quro,
    coalesce(b.vlr_subs, 0) vlr_daily,
    coalesce(c.subs_datapack, 0) packsub,
    coalesce(d.dse_cnt, 0) dse_cnt
from
    kpi_sum a
    left join
    max_date m
    on
        a.month_id = m.month_id
    left join
    kpi_snap b
    on
        a.month_id = b.month_id
    and a.circle = b.circle
    and a.region_circle = b.region_circle
    and a.kabkot_nm = b.kabkot_nm
    left join
    packsub c
    on
        a.month_id = c.month_id
    and a.circle = c.circle
    and a.region_circle = c.region_circle
    and a.kabkot_nm = c.kabkot_nm
    left join
    dse d
    on
        a.month_id = d.month_id
    and a.circle = d.circle
    and a.region_circle = d.region
    and a.kabkot_nm = d.kabkot_nm
    left join
    marketing.nc_kabu_mapping k
    on
        k.kabupaten = a.kabkot_nm
