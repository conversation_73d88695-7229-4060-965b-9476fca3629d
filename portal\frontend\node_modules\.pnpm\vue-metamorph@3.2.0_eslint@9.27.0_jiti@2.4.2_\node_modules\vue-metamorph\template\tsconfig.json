{
  "compilerOptions": {
    "esModuleInterop": true,
    "isolatedModules": true,
    "lib": [
      "ESNext",
    ],
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "noEmit": true,
    "noImplicitReturns": true,
    "noUncheckedIndexedAccess": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "strict": true,
    "target": "ESNext",
    "useDefineForClassFields": true,
    "types": ["node"]
  },
  "include": ["src"]
}
