#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/vitest@3.1.4_@types+node@22.15.21_@vitest+ui@3.1.4_jiti@2.4.2_lightningcss@1.30.1_stylus@0.57.0_yaml@2.8.0/node_modules/vitest/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/vitest@3.1.4_@types+node@22.15.21_@vitest+ui@3.1.4_jiti@2.4.2_lightningcss@1.30.1_stylus@0.57.0_yaml@2.8.0/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/vitest@3.1.4_@types+node@22.15.21_@vitest+ui@3.1.4_jiti@2.4.2_lightningcss@1.30.1_stylus@0.57.0_yaml@2.8.0/node_modules/vitest/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/vitest@3.1.4_@types+node@22.15.21_@vitest+ui@3.1.4_jiti@2.4.2_lightningcss@1.30.1_stylus@0.57.0_yaml@2.8.0/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../vitest.mjs" "$@"
else
  exec node  "$basedir/../../vitest.mjs" "$@"
fi
