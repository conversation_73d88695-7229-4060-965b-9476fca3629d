package pst_kabu

import (
	"gopkg.in/guregu/null.v4"
	"strconv"
)

type PstKabuKpi struct {
	SecondaryMn null.Float `db:"secondary_mn"`
	TertiaryMn  null.Float `db:"tertiary_mn"`
}

type PstKabuData struct {
	MonthID   string      `db:"month_id"`
	Period    string      `db:"period"`
	AsofDate  string      `db:"asof_date"`
	Circle    null.String `db:"circle"`
	Region    null.String `db:"region"`
	KabuFlag  null.String `db:"flag"`
	Brand     string      `db:"brand"`
	KabuCount int         `db:"kabu_cnt"`
	PstKabuKpi
}

func (p PstKabuData) GetColumns() []string {
	return []string{"month_id", "period", "asof_date", "circle", "region", "flag", "brand", "kabu_cnt", "secondary_mn", "tertiary_mn"}
}

func (p PstKabuData) GetRowValues() []string {
	secondary := ""
	if p.SecondaryMn.Valid {
		secondary = strconv.FormatFloat(p.SecondaryMn.ValueOrZero(), 'f', -1, 64)
	}

	tertiary := ""
	if p.TertiaryMn.Valid {
		tertiary = strconv.FormatFloat(p.TertiaryMn.ValueOrZero(), 'f', -1, 64)
	}

	return []string{p.MonthID, p.Period, p.AsofDate, p.Circle.String, p.Region.String, p.KabuFlag.String, p.Brand, strconv.Itoa(p.KabuCount), secondary, tertiary}
}

type RegionalPstKabuData struct {
	EntityType string
	EntityName string
	KabuCount  int
	MTD        *PstKabuKpi
	LMTD       *PstKabuKpi
	FM         map[string]*PstKabuKpi
}

type PstKabuReport struct {
	AsofDate        string
	FmList          []string
	CircleMap       map[string]*RegionalPstKabuData
	RegionalMap     map[string]*RegionalPstKabuData
	RegionalKabuMap map[string]map[string]*RegionalPstKabuData
	NationalData    *RegionalPstKabuData
	NationalKabuMap map[string]*RegionalPstKabuData
}
