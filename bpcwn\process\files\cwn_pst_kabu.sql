select
    case
        when a.mthf = 'mtd' then a.mth
        else from_timestamp(date_add(to_timestamp(a.dt_id,'yyyyMMdd'), interval -1 month), 'yyyyMM')
    end month_id,
    UPPER(a.mthf) period,
    case
        when a.mthf = 'mtd' then a.dt_id
        else from_timestamp(date_add(to_timestamp(a.dt_id,'yyyyMMdd'), interval -1 month), 'yyyyMMdd')
    end asof_date,
    b.circle,
    b.region,
    case when c.flag = 'Part of 203' then 'Loss Kabu' else c.flag end flag,
    'IOH' brand,
    count(distinct c.kabupaten) kabu_cnt,
    round(sum(case when a.parameter in ('Secondary', 'secondary') then a.amount/1000000 else null end), 3) secondary_mn,
    round(sum(case when a.parameter in ('Tertiary B#', 'tertiary') then a.amount/1000000 else null end), 3) tertiary_mn
from
    rdm.bai_tbl_kpi_dashboard_kec_wise_mtdlmtd_v2 a
    left join
    biadm.ref_kecamatan b
    on
        a.kec_unik = b.kec_kabkot
    left join
    vbt.nc_kabu_mapping c
    on
        b.kabkot_nm = c.kabupaten
where
    a.mthf in ('mtd', 'lmtd')
and a.dt_id = ${mtd_dt_id}
and a.parameter in
    (
        'Secondary', --im3
        'secondary', --3id
        'Tertiary B#', --im3
        'tertiary' --3id
    )
group by 1,2,3,4,5,6

union all

select
    a.mth month_id,
    'FM' period,
    a.dt_id,
    b.circle,
    b.region,
    case when c.flag = 'Part of 203' then 'Loss Kabu' else c.flag end flag,
    'IOH' brand,
    count(distinct c.kabupaten) kabu_cnt,
    round(sum(case when a.parameter in ('Secondary', 'secondary') then a.amount/1000000 else null end), 3) secondary_mn,
    round(sum(case when a.parameter in ('Tertiary B#', 'tertiary') then a.amount/1000000 else null end), 3) tertiary_mn
from
    rdm.bai_tbl_kpi_dashboard_kec_wise_mtdlmtd_v2 a
    left join
    biadm.ref_kecamatan b
    on
        a.kec_unik = b.kec_kabkot
    left join
    vbt.nc_kabu_mapping c
    on
        b.kabkot_nm = c.kabupaten
where
    a.mthf = 'mtd'
and a.dt_id >= from_timestamp(trunc(date_add(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), interval -3 month), 'month'), 'yyyyMMdd')
and a.dt_id < from_timestamp(trunc(to_timestamp(${mtd_dt_id}, 'yyyyMMdd'), 'month'), 'yyyyMMdd')
and a.dt_id = from_timestamp(date_add(trunc(date_add(to_timestamp(a.dt_id, 'yyyyMMdd'), interval 1 month), 'month'), interval -1 day), 'yyyyMMdd')
and a.parameter in
    (
        'Secondary', --im3
        'secondary', --3id
        'Tertiary B#', --im3
        'tertiary' --3id
    )
group by 1,2,3,4,5,6