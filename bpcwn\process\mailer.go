package process

import (
	"bytes"
	"context"
	"fmt"
	"html/template"
	"io"
	"os"
	"strings"
	"time"

	cfg "github.com/csee-pm/etl/shared/config"
	ctx "github.com/csee-pm/etl/shared/context"
	"github.com/csee-pm/etl/shared/notify"
)

type Attachment struct {
	FileName string
	Content  io.Reader
}

func SendReportEmail(c context.Context, reportFilePath string) error {
	conf := ctx.ExtractConfig(c)

	mailConfig, err := cfg.GetConfig[cfg.EmailServer](conf, "email_server")
	if err != nil {
		return fmt.Errorf("failed to get email server config. %s", err)
	}

	receivers := conf.GetStringSlice("report_recipients")

	mailer := notify.NewEmailNotifier(mailConfig.Host, mailConfig.Port, mailConfig.User, mailConfig.Pass, mailConfig.SenderAddress, mailConfig.SenderName, receivers...)

	title := "BPCS CWN Report"

	// open template cwn_report.html file from the embedded files and render it
	t, err := template.ParseFS(procFS, "files/cwn_report.html")
	if err != nil {
		return fmt.Errorf("failed to parse template. %s", err)
	}

	buf := new(bytes.Buffer)
	if err := t.Execute(buf, struct {
		ReportDate string
	}{
		ReportDate: time.Now().Format("01-Jan-2006 15:04:05"),
	}); err != nil {
		return fmt.Errorf("failed to execute template. %s", err)
	}

	htmlMsg := buf.String()

	reportFile, err := os.Open(reportFilePath)
	if err != nil {
		return fmt.Errorf("failed to open report file. %s", err)
	}
	defer reportFile.Close()

	attachment := notify.FileAttachment{
		FileName: "02.SND CWN Report.xlsx",
		Content:  reportFile,
	}

	return mailer.Notify(title, htmlMsg, attachment)
}

func SendReportEmailMessage(c context.Context, reportKey string, attachments ...Attachment) error {
	conf := ctx.ExtractConfig(c)

	mailConfig, err := cfg.GetConfig[cfg.EmailServer](conf, "email_server")
	if err != nil {
		return fmt.Errorf("failed to get email server config. %s", err)
	}

	receivers, err := getReportRecipients(c, reportKey)
	if err != nil {
		return fmt.Errorf("failed to get mail recipients. %s", err)
	}

	title, err := getReportTitle(c, reportKey)
	if err != nil {
		return err
	}

	htmlMsg, err := getReportEmailMessage(c, reportKey)
	if err != nil {
		return err
	}

	mailer := notify.NewEmailNotifier(mailConfig.Host, mailConfig.Port, mailConfig.User, mailConfig.Pass, mailConfig.SenderAddress, mailConfig.SenderName, receivers...)

	var attachs []notify.FileAttachment
	for _, a := range attachments {
		attachs = append(attachs, notify.FileAttachment{
			FileName: a.FileName,
			Content:  a.Content,
		})
	}

	return mailer.Notify(title, htmlMsg, attachs...)
}

func getReportRecipients(c context.Context, reportKey string) ([]string, error) {
	conf := ctx.ExtractConfig(c)

	defaultKey := "report_recipients.default"
	key := fmt.Sprintf("report_recipients.%s", reportKey)

	defaultRecipients := conf.GetStringSlice(defaultKey)
	recipients := conf.GetStringSlice(key)

	return append(defaultRecipients, recipients...), nil
}

func getReportTitle(c context.Context, reportKey string) (string, error) {
	switch strings.ToLower(reportKey) {
	case "distrib":
		return "Distribution Tracker Report", nil
	case "pst":
		return "PST Regional and New SEA Report", nil
	case "pst-kabu":
		return "New SEA Secondary & Tertiary Report", nil
	case "topkpi":
		return "Top KPI Report", nil
	case "sdp":
		return "SDP DSE Report", nil
	case "salmobo":
		return "Salmobo Report", nil
	case "ga-m2s":
		return "GA-M2S Tracking Report", nil
	default:
		return "", fmt.Errorf("unknown reportKey %q", reportKey)
	}
}

func getReportEmailMessage(c context.Context, reportKey string) (string, error) {
	switch strings.ToLower(reportKey) {
	case "distrib":
		return createDistribEmailMessage()
	case "pst":
		return createPSTEmailMessage()
	case "pst-kabu":
		return createPSTKabuEmailMessage()
	case "topkpi":
		return createTopKpiEmailMessage()
	case "sdp":
		return createSDPEmailMessage()
	case "salmobo":
		return createSalmoboEmailMessage()
	case "ga-m2s":
		return createGaM2sEmailMessage()
	default:
		return "", fmt.Errorf("unknown reportKey %q", reportKey)

	}
}

func createPSTEmailMessage() (string, error) {
	t, err := template.ParseFS(procFS, "files/email_pst_report.html")
	if err != nil {
		return "", err
	}

	buf := new(bytes.Buffer)
	if err := t.Execute(buf, struct {
		ReportDate string
	}{
		ReportDate: time.Now().Format("2-Jan-2006 15:04:05"),
	}); err != nil {
		return "", err
	}

	return buf.String(), nil
}

func createDistribEmailMessage() (string, error) {
	t, err := template.ParseFS(procFS, "files/email_distrib_report.html")
	if err != nil {
		return "", err
	}

	buf := new(bytes.Buffer)
	if err := t.Execute(buf, struct {
		ReportDate string
	}{
		ReportDate: time.Now().Format("2-Jan-2006 15:04:05"),
	}); err != nil {
		return "", err
	}

	return buf.String(), nil
}

func createTopKpiEmailMessage() (string, error) {
	t, err := template.ParseFS(procFS, "files/email_top_kpi_report.html")
	if err != nil {
		return "", err
	}

	buf := new(bytes.Buffer)
	if err := t.Execute(buf, struct {
		ReportDate string
	}{
		ReportDate: time.Now().Format("2-Jan-2006 15:04:05"),
	}); err != nil {
		return "", err
	}

	return buf.String(), nil
}

func createSDPEmailMessage() (string, error) {
	t, err := template.ParseFS(procFS, "files/email_sdp_dse_report.html")
	if err != nil {
		return "", err
	}

	buf := new(bytes.Buffer)
	if err := t.Execute(buf, struct {
		ReportDate string
	}{
		ReportDate: time.Now().Format("2-Jan-2006 15:04:05"),
	}); err != nil {
		return "", err
	}

	return buf.String(), nil
}

func createSalmoboEmailMessage() (string, error) {
	t, err := template.ParseFS(procFS, "files/email_salmobo_report.html")
	if err != nil {
		return "", err
	}

	buf := new(bytes.Buffer)
	if err := t.Execute(buf, struct {
		ReportDate string
	}{
		ReportDate: time.Now().Format("2-Jan-2006 15:04:05"),
	}); err != nil {
		return "", err
	}

	return buf.String(), nil
}

func createPSTKabuEmailMessage() (string, error) {
	t, err := template.ParseFS(procFS, "files/email_pst_kabu_report.html")
	if err != nil {
		return "", err
	}

	buf := new(bytes.Buffer)
	if err := t.Execute(buf, struct {
		ReportDate string
	}{
		ReportDate: time.Now().Format("2-Jan-2006 15:04:05"),
	}); err != nil {
		return "", err
	}

	return buf.String(), nil
}

func createGaM2sEmailMessage() (string, error) {
	t, err := template.ParseFS(procFS, "files/email_ga_m2s_report.html")
	if err != nil {
		return "", err
	}

	buf := new(bytes.Buffer)
	if err := t.Execute(buf, struct {
		ReportDate string
	}{
		ReportDate: time.Now().Format("2-Jan-2006 15:04:05"),
	}); err != nil {
		return "", err
	}

	return buf.String(), nil
}
