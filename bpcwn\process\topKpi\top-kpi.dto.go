package topKpi

import (
	"strconv"

	"gopkg.in/guregu/null.v4"
)

type TopKpi struct {
	RevenueMn float64  `db:"total_rev_gross_mn"`
	TrafficGb float64  `db:"traffic_gb"`
	Rgu90     int      `db:"rgs90d"`
	Rgu30     int      `db:"rgs30d"`
	Vlr       null.Int `db:"vlr_daily"`
	GrossAdds int      `db:"rgu_ga"`
	PackSubs  int      `db:"packsub"`
	Quro      int      `db:"quro"`
	Qsso      int      `db:"qsso"`
	// GaTryme   int      `db:"ga_tryme"`
	// Sp0325    int      `db:"sp0325"`
	OSA      float64 `db:"osa"`
	DseCount int     `db:"dse_cnt"`
}

type MtdTopKpiData struct {
	MonthID  string      `db:"month_id"`
	Period   string      `db:"period"`
	AsofDate string      `db:"asof_date"`
	Circle   null.String `db:"circle"`
	Region   null.String `db:"region"`
	Brand    string      `db:"brand"`
	TopKpi
}

func (mt MtdTopKpiData) GetColumns() []string {
	return []string{"month_id", "period", "asof_date", "circle", "region", "brand", "total_rev_gross_mn", "traffic_gb", "rgs90d", "rgs30d", "vlr_daily", "rgu_ga", "packsub", "quro", "qsso", "osa", "dse_cnt"}
}

func (mt MtdTopKpiData) GetRowValues() []string {
	revenue := strconv.FormatFloat(mt.RevenueMn, 'f', -1, 64)
	traffic := strconv.FormatFloat(mt.TrafficGb, 'f', -1, 64)
	rgs90 := strconv.Itoa(mt.Rgu90)
	rgs30 := strconv.Itoa(mt.Rgu30)
	vlr := strconv.Itoa(int(mt.Vlr.Int64))
	ga := strconv.Itoa(mt.GrossAdds)
	pack := strconv.Itoa(mt.PackSubs)
	quro := strconv.Itoa(mt.Quro)
	qsso := strconv.Itoa(mt.Qsso)
	osa := strconv.FormatFloat(mt.OSA, 'f', -1, 64)
	dse := strconv.Itoa(mt.DseCount)

	return []string{mt.MonthID, mt.Period, mt.AsofDate, mt.Circle.String, mt.Region.String, mt.Brand, revenue, traffic, rgs90, rgs30, vlr, ga, pack, quro, qsso, osa, dse}
}

type MtdTopKpiKabuData struct {
	MtdTopKpiData
	Kabupaten null.String `db:"kabupaten"`
	KabuFlag  null.String `db:"flag"`
}

func (mt MtdTopKpiKabuData) GetColumns() []string {
	return []string{"month_id", "period", "asof_date", "circle", "region", "brand", "kabupaten", "flag", "total_rev_gross_mn", "traffic_gb", "rgs90d", "rgs30d", "vlr_daily", "rgu_ga", "packsub", "quro", "qsso", "osa", "dse_cnt"}
}

func (mt MtdTopKpiKabuData) GetRowValues() []string {
	revenue := strconv.FormatFloat(mt.RevenueMn, 'f', -1, 64)
	traffic := strconv.FormatFloat(mt.TrafficGb, 'f', -1, 64)
	rgs90 := strconv.Itoa(mt.Rgu90)
	rgs30 := strconv.Itoa(mt.Rgu30)
	vlr := strconv.Itoa(int(mt.Vlr.Int64))
	ga := strconv.Itoa(mt.GrossAdds)
	pack := strconv.Itoa(mt.PackSubs)
	quro := strconv.Itoa(mt.Quro)
	qsso := strconv.Itoa(mt.Qsso)
	osa := strconv.FormatFloat(mt.OSA, 'f', -1, 64)
	dse := strconv.Itoa(mt.DseCount)

	return []string{mt.MonthID, mt.Period, mt.AsofDate, mt.Circle.String, mt.Region.String, mt.Brand, mt.Kabupaten.String, mt.KabuFlag.String, revenue, traffic, rgs90, rgs30, vlr, ga, pack, quro, qsso, osa, dse}
}

type MtdTopKpi struct {
	MonthID  string
	Brand    string
	Period   string
	AsofDate string
	TopKpi
}

type MtdTopKpiKabuFlag struct {
	MonthID  string
	Brand    string
	Flag     string
	Period   string
	AsofDate string
	TopKpi
}

type TopKpiReportData struct {
	Brand    string
	AsofDate string
	MTD      *MtdTopKpi
	LMTD     *MtdTopKpi
}

type TopKpiReport struct {
	IM3   TopKpiReportData
	Three TopKpiReportData
	IOH   TopKpiReportData
}

type TopKpiKabuReportData struct {
	Brand    string
	AsofDate string
	MTD      *MtdTopKpiKabuFlag
	LMTD     *MtdTopKpiKabuFlag
}

type TopKpiKabuReport struct {
	IM3   map[string]*TopKpiKabuReportData
	Three map[string]*TopKpiKabuReportData
	IOH   map[string]*TopKpiKabuReportData
}
