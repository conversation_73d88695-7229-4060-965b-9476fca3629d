package db

import (
	"database/sql"
	"time"
)

// CreateFile creates a new file record
func (db *DB) CreateFile(file *File) error {
	query := `
		INSERT INTO files (process_id, path, type, created_at, size)
		VALUES (?, ?, ?, ?, ?)
	`
	result, err := db.Exec(query, file.ProcessID, file.Path, file.Type, file.CreatedAt, file.Size)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}

	file.ID = id
	return nil
}

// GetFile retrieves a file by ID
func (db *DB) GetFile(id int64) (*File, error) {
	query := `SELECT id, process_id, path, type, created_at, size FROM files WHERE id = ?`
	
	var file File
	var processID sql.NullInt64
	
	err := db.QueryRow(query, id).Scan(
		&file.ID, &processID, &file.Path, &file.Type, &file.CreatedAt, &file.Size,
	)
	
	if err != nil {
		return nil, err
	}
	
	if processID.Valid {
		file.ProcessID = processID.Int64
	}
	
	return &file, nil
}

// ListFiles retrieves all files
func (db *DB) ListFiles() ([]*File, error) {
	query := `SELECT id, process_id, path, type, created_at, size FROM files ORDER BY created_at DESC`
	
	rows, err := db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var files []*File
	
	for rows.Next() {
		var file File
		var processID sql.NullInt64
		
		err := rows.Scan(
			&file.ID, &processID, &file.Path, &file.Type, &file.CreatedAt, &file.Size,
		)
		
		if err != nil {
			return nil, err
		}
		
		if processID.Valid {
			file.ProcessID = processID.Int64
		}
		
		files = append(files, &file)
	}
	
	return files, nil
}

// ListFilesByProcess retrieves all files for a specific process
func (db *DB) ListFilesByProcess(processID int64) ([]*File, error) {
	query := `SELECT id, process_id, path, type, created_at, size FROM files WHERE process_id = ? ORDER BY created_at DESC`
	
	rows, err := db.Query(query, processID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var files []*File
	
	for rows.Next() {
		var file File
		var pID sql.NullInt64
		
		err := rows.Scan(
			&file.ID, &pID, &file.Path, &file.Type, &file.CreatedAt, &file.Size,
		)
		
		if err != nil {
			return nil, err
		}
		
		if pID.Valid {
			file.ProcessID = pID.Int64
		}
		
		files = append(files, &file)
	}
	
	return files, nil
}

// DeleteFile deletes a file record
func (db *DB) DeleteFile(id int64) error {
	query := `DELETE FROM files WHERE id = ?`
	_, err := db.Exec(query, id)
	return err
}

// ScanForFiles scans the work directory for files and adds them to the database
func (db *DB) ScanForFiles(workDir string, processID int64) error {
	// This is a placeholder for the actual implementation
	// In a real implementation, you would:
	// 1. Scan the work directory for files
	// 2. For each file, check if it's already in the database
	// 3. If not, add it to the database
	
	// For now, we'll just add a dummy file for demonstration
	file := &File{
		ProcessID: processID,
		Path:      workDir + "/example.csv",
		Type:      "csv",
		CreatedAt: time.Now(),
		Size:      1024,
	}
	
	return db.CreateFile(file)
}
