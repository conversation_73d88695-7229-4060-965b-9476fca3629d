package sdpRpt

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"io/fs"
	"math"
	"os"
	"slices"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"gopkg.in/guregu/null.v4"

	cfg "github.com/csee-pm/etl/bpcwn/config"
	"github.com/csee-pm/etl/shared/channel"
	ctx "github.com/csee-pm/etl/shared/context"
	etlDb "github.com/csee-pm/etl/shared/db"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/csee-pm/etl/shared/utils/xlutil"
	"github.com/xuri/excelize/v2"
)

type SdpProcess struct {
	procFS fs.ReadFileFS
}

func NewSdpProcess(procFS fs.ReadFileFS) SdpProcess {
	return SdpProcess{
		procFS: procFS,
	}
}

func (sdp SdpProcess) GetReportData(c context.Context) (*SdpDseReport, error) {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	logger.Info("Running SDP Report")
	workDate := time.Now().AddDate(0, 0, -2)

	var err error
	if conf.Get("etl.mtd_date") != nil {
		mtdDate := conf.GetString("etl.mtd_date")
		workDate, err = time.Parse("20060102", mtdDate)
		if err != nil {
			return nil, fmt.Errorf("failed to parse mtd date. %s", err)
		}
	}

	conf.Set("work_date", workDate)

	cCancel, cancel := context.WithCancel(c)
	defer cancel()

	var dseData []DseMtdData
	var dseReport *DseReport
	var sdpData []SdpMtdData
	var sdpReport *SdpReport

	var wg sync.WaitGroup

	wg.Add(1)
	dseResult := channel.RunAsyncContext(cCancel, func() ([]DseMtdData, error) {
		if cfg.UseDSEFromFile != "" {
			return sdp.GetDSEFromFile(cCancel, cfg.UseDSEFromFile)
		}
		return sdp.GetDseData(cCancel)
	})

	go func() {
		defer wg.Done()
		for res := range dseResult {
			res.Map(func(data []DseMtdData) {
				dseData = data
				dseReport, err = sdp.processDseData(data)
				if err != nil {
					logger.Error("failed to process DSE data", "error", err)
					cancel()
				}
			}).MapErr(func(er error) {
				err = fmt.Errorf("failed to get DSE data. %s", er)
				logger.Debug("calling cancel", "caller", "sdp.GetDseData", "error", err)
				cancel()
			})
		}
	}()

	wg.Add(1)
	sdpResult := channel.RunAsyncContext(cCancel, func() ([]SdpMtdData, error) {
		if cfg.UseSDPFromFile != "" {
			return sdp.GetSdpFromFile(cCancel, cfg.UseSDPFromFile)
		}
		return sdp.GetSdpData(cCancel)
	})

	go func() {
		defer wg.Done()
		for res := range sdpResult {
			res.Map(func(data []SdpMtdData) {
				sdpData = data
				sdpReport, err = sdp.processSdpData(data)
				if err != nil {
					logger.Error("failed to process SDP data", "error", err)
					cancel()
				}
			}).MapErr(func(er error) {
				err = fmt.Errorf("failed to get SDP data. %s", er)
				logger.Debug("calling cancel", "caller", "sdp.GetSdpData", "error", err)
				cancel()
			})
		}
	}()

	wg.Wait()
	if err != nil {
		return nil, err
	}

	workDir := ctx.ExtractWorkDir(c)

	dseCsvFilePath := fmt.Sprintf("%s/dse_%s.csv", workDir, time.Now().Format("20060102150405"))
	err = utils.WriteToCsv(dseCsvFilePath, dseData)
	if err != nil {
		logger.Error("failed to write CSV file", "path", dseCsvFilePath, "error", err)
	} else {
		logger.Info("CSV file written", "path", dseCsvFilePath)
	}

	sdpCsvFilePath := fmt.Sprintf("%s/sdp_%s.csv", workDir, time.Now().Format("20060102150405"))
	err = utils.WriteToCsv(sdpCsvFilePath, sdpData)
	if err != nil {
		logger.Error("failed to write CSV file", "path", sdpCsvFilePath, "error", err)
	} else {
		logger.Info("CSV file written", "path", sdpCsvFilePath)
	}

	return &SdpDseReport{
		Sdp: sdpReport,
		Dse: dseReport,
	}, nil
}

func (sdp SdpProcess) GetDseData(c context.Context) ([]DseMtdData, error) {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	mtdDate := conf.GetTime("work_date")
	mtdDateInt, err := strconv.Atoi(mtdDate.Format("20060102"))
	if err != nil {
		return nil, err
	}

	cCancel, cancel := context.WithCancel(c)
	defer cancel()

	var wg sync.WaitGroup
	var im3Data []DseMtdData
	var threeData []DseMtdData

	wg.Add(1)
	im3Result := channel.RunAsyncContext(cCancel, func() ([]DseMtdData, error) {
		return sdp.getIm3DseData(cCancel, mtdDate.Format("20060102"))
	})

	go func() {
		defer wg.Done()
		for res := range im3Result {
			res.Map(func(data []DseMtdData) {
				im3Data = data
			}).MapErr(func(er error) {
				err = fmt.Errorf("failed to get IM3 DSE data. %s", er)
				logger.Debug("calling cancel", "caller", "sdp.getIm3DseData", "error", err)
				cancel()
			})
		}
	}()

	wg.Add(1)
	triResult := channel.RunAsyncContext(cCancel, func() ([]DseMtdData, error) {
		return sdp.get3idDseData(cCancel, mtdDateInt)
	})

	go func() {
		defer wg.Done()
		for res := range triResult {
			res.Map(func(data []DseMtdData) {
				threeData = data
			}).MapErr(func(er error) {
				err = fmt.Errorf("failed to get 3ID DSE data. %s", er)
				logger.Debug("calling cancel", "caller", "sdp.get3idDseData", "error", err)
				cancel()
			})
		}
	}()

	wg.Wait()
	if err != nil {
		return nil, err
	}

	return append(im3Data, threeData...), nil
}

func (sdp SdpProcess) GetDSEFromFile(c context.Context, fpath string) ([]DseMtdData, error) {
	f, err := os.Open(fpath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	cr := csv.NewReader(f)
	cr.Comma = ','
	cr.LazyQuotes = true

	cr.Read()
	var data []DseMtdData
	for {
		record, err := cr.Read()
		if err == io.EOF {
			break
		}

		if err != nil {
			return nil, err
		}

		ga, err := strconv.Atoi(record[7])
		if err != nil {
			return nil, fmt.Errorf("failed to parse GA. %s", err)
		}

		netSecMn, err := strconv.ParseFloat(record[8], 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse Net Secondary Mn. %s", err)
		}

		dayNum, err := strconv.Atoi(record[9])
		if err != nil {
			return nil, fmt.Errorf("failed to parse Day Num. %s", err)
		}

		data = append(data, DseMtdData{
			MonthID:        record[0],
			Period:         record[1],
			AsofDate:       record[2],
			Brand:          record[3],
			Circle:         null.StringFrom(record[4]),
			Region:         null.StringFrom(record[5]),
			Dse:            record[6],
			GrossAdds:      ga,
			NetSecondaryMn: netSecMn,
			DayNum:         dayNum,
		})
	}

	return data, nil
}

func (sdp SdpProcess) getIm3DseData(c context.Context, mtdDate string) ([]DseMtdData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := sdp.procFS.ReadFile("files/im3_dse.sql")
	if err != nil {
		return nil, fmt.Errorf("failed to read im3_dse.sql. %s", err)
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_id": {Name: "mtd_dt_id", Value: mtdDate},
	}

	logger.Info("Getting IM3 DSE data")
	return etlProc.QueryImpalaData[DseMtdData](c, string(buf), params)
}

func (sdp SdpProcess) get3idDseData(c context.Context, mtdDate int) ([]DseMtdData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := sdp.procFS.ReadFile("files/3id_dse.sql")
	if err != nil {
		return nil, fmt.Errorf("failed to read 3id_dse.sql. %s", err)
	}

	logger.Info("getting 3ID DSE data")

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_int": {Name: "mtd_dt_int", Value: mtdDate},
	}

	return etlProc.QueryGreenplumData[DseMtdData](c, string(buf), params)
}

func (sdp SdpProcess) GetSdpData(c context.Context) ([]SdpMtdData, error) {
	conf := ctx.ExtractConfig(c)
	logger := ctx.ExtractLogger(c)

	mtdDate := conf.GetTime("work_date")
	mtdDateInt, err := strconv.Atoi(mtdDate.Format("20060102"))
	if err != nil {
		return nil, err
	}

	cCancel, cancel := context.WithCancel(c)
	defer cancel()

	var wg sync.WaitGroup
	var im3Data []SdpMtdData
	var threeData []SdpMtdData

	wg.Add(1)
	im3Result := channel.RunAsyncContext(cCancel, func() ([]SdpMtdData, error) {
		return sdp.getIm3SdpData(cCancel, mtdDate.Format("20060102"))
	})

	go func() {
		defer wg.Done()
		for res := range im3Result {
			res.Map(func(data []SdpMtdData) {
				im3Data = data
			}).MapErr(func(er error) {
				err = fmt.Errorf("failed to get IM3 SDP data. %s", er)
				logger.Debug("calling cancel", "caller", "sdp.getIm3SdpData", "error", err)
				cancel()
			})
		}
	}()

	wg.Add(1)
	triResult := channel.RunAsyncContext(cCancel, func() ([]SdpMtdData, error) {
		return sdp.get3idSdpData(cCancel, mtdDateInt)
	})

	go func() {
		defer wg.Done()
		for res := range triResult {
			res.Map(func(data []SdpMtdData) {
				threeData = data
			}).MapErr(func(er error) {
				err = fmt.Errorf("failed to get 3ID SDP data. %s", er)
				logger.Debug("calling cancel", "caller", "sdp.get3idSdpData", "error", err)
				cancel()
			})
		}
	}()

	wg.Wait()
	if err != nil {
		return nil, err
	}

	return append(im3Data, threeData...), nil
}

func (sdp SdpProcess) GetSdpFromFile(c context.Context, fpath string) ([]SdpMtdData, error) {
	f, err := os.Open(fpath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	cr := csv.NewReader(f)
	cr.Comma = ','
	cr.LazyQuotes = true
	cr.Read()

	var data []SdpMtdData
	for {
		record, err := cr.Read()
		if err == io.EOF {
			break
		}

		if err != nil {
			return nil, err
		}

		ga, err := strconv.Atoi(record[8])
		if err != nil {
			return nil, fmt.Errorf("failed to parse GA. %s", err)
		}

		netSecMn, err := strconv.ParseFloat(record[9], 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse Net Secondary Mn. %s", err)
		}

		dayNum, err := strconv.Atoi(record[10])
		if err != nil {
			return nil, fmt.Errorf("failed to parse Day Num. %s", err)
		}

		data = append(data, SdpMtdData{
			MonthID:        record[0],
			Period:         record[1],
			AsofDate:       record[2],
			Brand:          record[3],
			Circle:         null.StringFrom(record[4]),
			Region:         null.StringFrom(record[5]),
			SdpFlag:        record[6],
			Sdp:            record[7],
			GrossAdds:      ga,
			NetSecondaryMn: netSecMn,
			DayNum:         dayNum,
		})
	}

	return data, nil
}

func (sdp SdpProcess) getIm3SdpData(c context.Context, mtdDate string) ([]SdpMtdData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := sdp.procFS.ReadFile("files/im3_sdp.sql")
	if err != nil {
		return nil, fmt.Errorf("failed to read im3_sdp.sql. %s", err)
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_id": {Name: "mtd_dt_id", Value: mtdDate},
	}

	logger.Info("Getting IM3 SDP data")
	return etlProc.QueryImpalaData[SdpMtdData](c, string(buf), params)
}

func (sdp SdpProcess) get3idSdpData(c context.Context, mtdDate int) ([]SdpMtdData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := sdp.procFS.ReadFile("files/3id_sdp.sql")
	if err != nil {
		return nil, fmt.Errorf("failed to read 3id_sdp.sql. %s", err)
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_int": {Name: "mtd_dt_int", Value: mtdDate},
	}

	logger.Info("Getting 3ID SDP data")
	return etlProc.QueryGreenplumData[SdpMtdData](c, string(buf), params)
}

var (
	ga_dse_bins    = []int{-math.MaxInt, 0, 150, 250, 500, math.MaxInt}
	ga_dse_labels  = []string{"0", "1-150", "150-250", "250-500", ">500"}
	sec_dse_bins   = []int{-math.MaxInt, 10, 25, 50, 100, math.MaxInt}
	sec_dse_labels = []string{"<10", "10-25", "25-50", "50-100", ">100"}

	ga_sdp_bins    = []int{-math.MaxInt, 30, 100, 350, 500, math.MaxInt}
	ga_sdp_labels  = []string{"<30", "30-100", "100-350", "350-500", ">500"}
	sec_sdp_bins   = []int{-math.MaxInt, 25, 50, 100, 150, math.MaxInt}
	sec_sdp_labels = []string{"<25", "25-50", "50-100", "100-150", ">150"}
)

func (sdp SdpProcess) processDseData(data []DseMtdData) (*DseReport, error) {
	logger := ctx.ExtractLogger(context.Background())

	regMapim3 := make(map[string]*RegionalReportData)
	regMap3id := make(map[string]*RegionalReportData)
	regMapIOH := make(map[string]*RegionalReportData)

	cirMapim3 := make(map[string]*RegionalReportData)
	cirMap3id := make(map[string]*RegionalReportData)
	cirMapIOH := make(map[string]*RegionalReportData)

	nationalIm3 := newRegionalReportData("NATIONAL", "INDONESIA", "IM3")
	national3id := newRegionalReportData("NATIONAL", "INDONESIA", "3ID")
	nationalIOH := newRegionalReportData("NATIONAL", "INDONESIA", "IOH")

	fmMap := make(map[string]struct{})

	var asofDate time.Time
	var err error

	for _, d := range data {
		brand := d.Brand

		circle := d.Circle.String
		if circle == "JAYA" {
			circle = "JAKARTA RAYA"
		}

		region := d.Region.String
		period := d.Period

		var regMap = regMapim3
		var cirMap = cirMapim3
		var national = nationalIm3

		if brand == "3ID" {
			regMap = regMap3id
			cirMap = cirMap3id
			national = national3id
		}

		if _, ok := regMap[region]; !ok {
			regMap[region] = newRegionalReportData("REGION", region, brand)
		}

		if _, ok := regMapIOH[region]; !ok {
			regMapIOH[region] = newRegionalReportData("REGION", region, "IOH")
		}

		if _, ok := cirMap[circle]; !ok {
			cirMap[circle] = newRegionalReportData("CIRCLE", circle, brand)
		}

		if _, ok := cirMapIOH[circle]; !ok {
			cirMapIOH[circle] = newRegionalReportData("CIRCLE", circle, "IOH")
		}

		kpiData := PartnerKpiData{
			MonthID:        d.MonthID,
			GrossAdds:      d.GrossAdds,
			NetSecondaryMn: d.NetSecondaryMn,
			DayNum:         d.DayNum,
		}

		switch period {
		case "MTD":
			asofDate, err = time.Parse("20060102", d.AsofDate)
			if err != nil {
				logger.Error("failed to parse asof date", "date", d.AsofDate, "error", err)
			}
			sdp.processDseMtdData(regMap[region].MTD, kpiData)
			sdp.processDseMtdData(cirMap[circle].MTD, kpiData)
			sdp.processDseMtdData(regMapIOH[region].MTD, kpiData)
			sdp.processDseMtdData(cirMapIOH[circle].MTD, kpiData)
			sdp.processDseMtdData(national.MTD, kpiData)
			sdp.processDseMtdData(nationalIOH.MTD, kpiData)
		case "LMTD":
			sdp.processDseMtdData(regMap[region].LMTD, kpiData)
			sdp.processDseMtdData(cirMap[circle].LMTD, kpiData)
			sdp.processDseMtdData(regMapIOH[region].LMTD, kpiData)
			sdp.processDseMtdData(cirMapIOH[circle].LMTD, kpiData)
			sdp.processDseMtdData(national.LMTD, kpiData)
			sdp.processDseMtdData(nationalIOH.LMTD, kpiData)
		case "FM":
			sdp.processFmData(regMap[region].FM, kpiData)
			sdp.processFmData(cirMap[circle].FM, kpiData)
			sdp.processFmData(regMapIOH[region].FM, kpiData)
			sdp.processFmData(cirMapIOH[circle].FM, kpiData)
			sdp.processFmData(national.FM, kpiData)
			sdp.processFmData(nationalIOH.FM, kpiData)
			fmMap[d.MonthID] = struct{}{}
		}
	}

	fmList := utils.MapToList(fmMap, func(key string, value struct{}) string {
		return key
	})

	slices.Sort(fmList)

	return &DseReport{
		AsofDate: asofDate,
		IM3: &PartnerReportData{
			FmList:       fmList,
			CircleData:   cirMapim3,
			RegionalData: regMapim3,
			NationalData: nationalIm3,
		},
		Three: &PartnerReportData{
			FmList:       fmList,
			CircleData:   cirMap3id,
			RegionalData: regMap3id,
			NationalData: national3id,
		},
		IOH: &PartnerReportData{
			FmList:       fmList,
			CircleData:   cirMapIOH,
			RegionalData: regMapIOH,
			NationalData: nationalIOH,
		},
	}, nil
}

func (sdp SdpProcess) processSdpData(data []SdpMtdData) (*SdpReport, error) {
	logger := ctx.ExtractLogger(context.Background())

	regMapim3 := make(map[string]*RegionalReportData)
	regMap3id := make(map[string]*RegionalReportData)
	regMapIOH := make(map[string]*RegionalReportData)

	cirMapim3 := make(map[string]*RegionalReportData)
	cirMap3id := make(map[string]*RegionalReportData)
	cirMapIOH := make(map[string]*RegionalReportData)

	nationalIm3 := newRegionalReportData("NATIONAL", "INDONESIA", "IM3")
	national3id := newRegionalReportData("NATIONAL", "INDONESIA", "3ID")
	nationalIOH := newRegionalReportData("NATIONAL", "INDONESIA", "IOH")

	fmMap := make(map[string]struct{})

	var asofDate time.Time
	var err error

	for _, d := range data {
		brand := d.Brand

		circle := d.Circle.String
		if circle == "JAYA" {
			circle = "JAKARTA RAYA"
		}

		region := d.Region.String
		period := d.Period
		flag := d.SdpFlag

		if flag == "MPC" {
			continue
		}

		if flag == "MP3" {
			continue
		}

		var regMap = regMapim3
		var cirMap = cirMapim3
		var national = nationalIm3

		if brand == "3ID" {
			regMap = regMap3id
			cirMap = cirMap3id
			national = national3id
		}

		if _, ok := regMap[region]; !ok {
			regMap[region] = newRegionalReportData("REGION", region, brand)
		}

		if _, ok := regMapIOH[region]; !ok {
			regMapIOH[region] = newRegionalReportData("REGION", region, "IOH")
		}

		if _, ok := cirMap[circle]; !ok {
			cirMap[circle] = newRegionalReportData("CIRCLE", circle, brand)
		}

		if _, ok := cirMapIOH[circle]; !ok {
			cirMapIOH[circle] = newRegionalReportData("CIRCLE", circle, "IOH")
		}

		kpiData := PartnerKpiData{
			MonthID:        d.MonthID,
			GrossAdds:      d.GrossAdds,
			NetSecondaryMn: d.NetSecondaryMn,
			DayNum:         d.DayNum,
		}

		switch period {
		case "MTD":
			asofDate, err = time.Parse("20060102", d.AsofDate)
			if err != nil {
				logger.Error("failed to parse asof date", "date", d.AsofDate, "error", err)
			}
			sdp.processSdpMtdData(regMap[region].MTD, kpiData)
			sdp.processSdpMtdData(cirMap[circle].MTD, kpiData)
			sdp.processSdpMtdData(regMapIOH[region].MTD, kpiData)
			sdp.processSdpMtdData(cirMapIOH[circle].MTD, kpiData)
			sdp.processSdpMtdData(national.MTD, kpiData)
			sdp.processSdpMtdData(nationalIOH.MTD, kpiData)
		case "LMTD":
			sdp.processSdpMtdData(regMap[region].LMTD, kpiData)
			sdp.processSdpMtdData(cirMap[circle].LMTD, kpiData)
			sdp.processSdpMtdData(regMapIOH[region].LMTD, kpiData)
			sdp.processSdpMtdData(cirMapIOH[circle].LMTD, kpiData)
			sdp.processSdpMtdData(national.LMTD, kpiData)
			sdp.processSdpMtdData(nationalIOH.LMTD, kpiData)
		case "FM":
			sdp.processFmData(regMap[region].FM, kpiData)
			sdp.processFmData(cirMap[circle].FM, kpiData)
			sdp.processFmData(regMapIOH[region].FM, kpiData)
			sdp.processFmData(cirMapIOH[circle].FM, kpiData)
			sdp.processFmData(national.FM, kpiData)
			sdp.processFmData(nationalIOH.FM, kpiData)
			fmMap[d.MonthID] = struct{}{}
		}
	}

	fmList := utils.MapToList(fmMap, func(key string, value struct{}) string {
		return key
	})

	slices.Sort(fmList)

	return &SdpReport{
		AsofDate: asofDate,
		IM3: &PartnerReportData{
			FmList:       fmList,
			CircleData:   cirMapim3,
			RegionalData: regMapim3,
			NationalData: nationalIm3,
		},
		Three: &PartnerReportData{
			FmList:       fmList,
			CircleData:   cirMap3id,
			RegionalData: regMap3id,
			NationalData: national3id,
		},
		IOH: &PartnerReportData{
			FmList:       fmList,
			CircleData:   cirMapIOH,
			RegionalData: regMapIOH,
			NationalData: nationalIOH,
		},
	}, nil
}

func (sdp SdpProcess) processDseMtdData(kpiData *RegionalMtdKpiData, d PartnerKpiData) {
	gaDaily := d.GrossAdds / d.DayNum
	secDaily := d.NetSecondaryMn / float64(d.DayNum)

	gaSlab := getDseGaBinLabel(d.GrossAdds)
	gaDailySlab := getDseGaDailyBinLabel(gaDaily)
	secSlab := getDseSecBinLabel(d.NetSecondaryMn)
	secDailySlab := getDseSecDailyBinLabel(secDaily)

	kpiData.GrossAdds += d.GrossAdds
	kpiData.NetSecondaryMn += d.NetSecondaryMn
	kpiData.GaSlabs[gaSlab]++
	kpiData.GaDailySlabs[gaDailySlab]++
	kpiData.SecondarySlabs[secSlab]++
	kpiData.SecondaryDailySlabs[secDailySlab]++
	kpiData.PartnerCount++
}

func (sdp SdpProcess) processSdpMtdData(kpiData *RegionalMtdKpiData, d PartnerKpiData) {
	gaSlab := getSdpGaBinLabel(d.GrossAdds)
	secSlab := getSdpSecBinLabel(d.NetSecondaryMn)

	kpiData.GrossAdds += d.GrossAdds
	kpiData.NetSecondaryMn += d.NetSecondaryMn
	kpiData.GaSlabs[gaSlab]++
	kpiData.SecondarySlabs[secSlab]++
	kpiData.PartnerCount++
}

func (sdp SdpProcess) processFmData(kpiData map[string]*RegionalFmKpiData, d PartnerKpiData) {
	monthID := d.MonthID
	if _, ok := kpiData[monthID]; !ok {
		kpiData[monthID] = &RegionalFmKpiData{
			MonthID: monthID,
			//Brand:          d.Brand,
			GrossAdds:      0,
			NetSecondaryMn: 0,
		}
	}

	kpiData[monthID].GrossAdds += d.GrossAdds
	kpiData[monthID].NetSecondaryMn += d.NetSecondaryMn
}

func newRegionalReportData(regType, regName, brand string) *RegionalReportData {
	return &RegionalReportData{
		RegionType: regType,
		RegionName: regName,
		Brand:      brand,
		MTD: &RegionalMtdKpiData{
			Period: "MTD",
			//RegionType:          regType,
			//RegionName:          regName,
			//Brand:               brand,
			GrossAdds:           0,
			NetSecondaryMn:      0,
			GaSlabs:             make(map[string]int),
			GaDailySlabs:        make(map[string]int),
			SecondarySlabs:      make(map[string]int),
			SecondaryDailySlabs: make(map[string]int),
			PartnerCount:        0,
		},
		LMTD: &RegionalMtdKpiData{
			Period: "LMTD",
			//RegionType:          regType,
			//RegionName:          regName,
			//Brand:               brand,
			GrossAdds:           0,
			NetSecondaryMn:      0,
			GaSlabs:             make(map[string]int),
			GaDailySlabs:        make(map[string]int),
			SecondarySlabs:      make(map[string]int),
			SecondaryDailySlabs: make(map[string]int),
			PartnerCount:        0,
		},
		FM: make(map[string]*RegionalFmKpiData),
	}
}

var (
	dse_ga_start_col        = 5
	dse_sec_start_col       = 13
	dse_ga_slab_start_col   = 21
	dse_sec_slab_start_col  = 28
	dse_ga_daily_start_col  = 35
	dse_sec_daily_start_col = 39
	dse_entity_col          = 2
	DSE_SUMMARY_SHEET       = "Summary DSE Productivity"

	sdp_ga_start_col       = 4
	sdp_sec_start_col      = 12
	sdp_ga_slab_start_col  = 20
	sdp_sec_slab_start_col = 27
	sdp_entity_col         = 2
	SDP_SUMMARY_SHEET      = "Summary SDP-3Kiosk"
)

func (sdp SdpProcess) WriteDseReport(xl *excelize.File, data *DseReport) error {
	startIM3 := xlutil.Cell(10, 2)
	start3ID := xlutil.Cell(30, 2)
	//startIOH := xlutil.Cell(54, 2)

	dIM3 := data.IM3
	d3ID := data.Three
	//dIOH := data.IOH

	// fill asof date
	asofDate := data.AsofDate.Format("02-Jan")
	if err := xl.SetCellValue(DSE_SUMMARY_SHEET, "B8", fmt.Sprintf("DSE - IM3 as of %s", asofDate)); err != nil {
		return err
	}

	if err := xl.SetCellValue(DSE_SUMMARY_SHEET, "B28", fmt.Sprintf("DSE - 3ID as of %s", asofDate)); err != nil {
		return err
	}

	// do IM3
	if err := sdp.writeDseReport(xl, dIM3, startIM3.Row, startIM3.Row+16); err != nil {
		return err
	}

	// do 3ID
	if err := sdp.writeDseReport(xl, d3ID, start3ID.Row, start3ID.Row+16); err != nil {
		return err
	}

	// do IOH
	//if err := sdp.writeDseReport(xl, dIOH, startIOH.Row, startIOH.Row+16); err != nil {
	//	return err
	//}

	return nil
}

func (sdp SdpProcess) writeDseReport(xl *excelize.File, data *PartnerReportData, startRow, endRow int) error {
	fmList := data.FmList
	sort.Slice(fmList, func(i, j int) bool {
		return fmList[i] > fmList[j]
	})

	for i, mth := range fmList {
		if i > 2 {
			break
		}

		mthDate, err := time.Parse("200601", mth)
		if err != nil {
			return err
		}

		mthName := mthDate.Format("Jan-06")
		if err := xl.SetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(startRow-1, dse_ga_start_col+(2-i)).Address(), mthName); err != nil {
			return err
		}

		if err := xl.SetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(startRow-1, dse_sec_start_col+(2-i)).Address(), mthName); err != nil {
			return err
		}

	}

	for r := startRow; r <= endRow; r++ {
		entity, err := xl.GetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(r, dse_entity_col).Address())
		if err != nil {
			return err
		}
		entity = strings.TrimSpace(entity)
		if entity == "" {
			continue
		}

		if entity == "JAYA" {
			entity = "JAKARTA RAYA"
		}

		var regData *RegionalReportData
		if _, ok := data.CircleData[entity]; ok {
			regData = data.CircleData[entity]
		}

		if _, ok := data.RegionalData[entity]; ok {
			regData = data.RegionalData[entity]
		}

		if entity == "INDONESIA" {
			regData = data.NationalData
		}

		if regData == nil {
			continue
		}

		// fill Full Month data
		for i, mth := range fmList {
			if i > 2 {
				break
			}

			gaK := float64(regData.FM[mth].GrossAdds) / 1000
			gaCol := dse_ga_start_col + (2 - i)
			secBn := regData.FM[mth].NetSecondaryMn / 1000
			secCol := dse_sec_start_col + (2 - i)

			if err := xl.SetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(r, gaCol).Address(), gaK); err != nil {
				return err
			}

			if err := xl.SetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(r, secCol).Address(), secBn); err != nil {
				return err
			}
		}

		mtdGaK := float64(regData.MTD.GrossAdds) / 1000
		lmtdGaK := float64(regData.LMTD.GrossAdds) / 1000
		gaAbs := mtdGaK - lmtdGaK
		gaPct := 0.0
		if lmtdGaK != 0 {
			gaPct = float64(mtdGaK-lmtdGaK) / float64(lmtdGaK)
		}

		mtdSecBn := regData.MTD.NetSecondaryMn / 1000
		lmtdSecBn := regData.LMTD.NetSecondaryMn / 1000
		secAbs := mtdSecBn - lmtdSecBn
		secPct := (mtdSecBn - lmtdSecBn) / lmtdSecBn

		if err := xl.SetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(r, dse_ga_start_col+4).Address(), mtdGaK); err != nil {
			return err
		}

		if err := xl.SetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(r, dse_ga_start_col+3).Address(), lmtdGaK); err != nil {
			return err
		}

		if err := xl.SetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(r, dse_ga_start_col+5).Address(), gaAbs); err != nil {
			return err
		}

		if err := xl.SetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(r, dse_ga_start_col+6).Address(), gaPct); err != nil {
			return err
		}

		if err := xl.SetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(r, dse_sec_start_col+4).Address(), mtdSecBn); err != nil {
			return err
		}

		if err := xl.SetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(r, dse_sec_start_col+3).Address(), lmtdSecBn); err != nil {
			return err
		}

		if err := xl.SetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(r, dse_sec_start_col+5).Address(), secAbs); err != nil {
			return err
		}

		if err := xl.SetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(r, dse_sec_start_col+6).Address(), secPct); err != nil {
			return err
		}

		// fill GA slab data
		totalDse := 0
		for i, slab := range ga_dse_labels {
			mtdVal := regData.MTD.GaSlabs[slab]
			totalDse += mtdVal
			if err := xl.SetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(r, dse_ga_slab_start_col+i).Address(), mtdVal); err != nil {
				return err
			}
		}
		if err := xl.SetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(r, dse_ga_slab_start_col+len(ga_dse_labels)).Address(), totalDse); err != nil {
			return err
		}

		// fill GA daily slab data
		totalDse = 0
		for i, slab := range []string{"<12", ">=12"} {
			mtdVal := regData.MTD.GaDailySlabs[slab]
			totalDse += mtdVal
			if err := xl.SetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(r, dse_ga_daily_start_col+i).Address(), mtdVal); err != nil {
				return err
			}
		}
		if err := xl.SetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(r, dse_ga_daily_start_col+2).Address(), totalDse); err != nil {
			return err
		}

		// fill Secondary slab data
		totalDse = 0
		for i, slab := range sec_dse_labels {
			mtdVal := regData.MTD.SecondarySlabs[slab]
			totalDse += mtdVal
			if err := xl.SetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(r, dse_sec_slab_start_col+i).Address(), mtdVal); err != nil {
				return err
			}
		}
		if err := xl.SetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(r, dse_sec_slab_start_col+len(sec_dse_labels)).Address(), totalDse); err != nil {
			return err
		}

		// fill Secondary daily slab data
		totalDse = 0
		for i, slab := range []string{"<5", ">=5"} {
			mtdVal := regData.MTD.SecondaryDailySlabs[slab]
			totalDse += mtdVal
			if err := xl.SetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(r, dse_sec_daily_start_col+i).Address(), mtdVal); err != nil {
				return err
			}
		}

		if err := xl.SetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(r, dse_sec_daily_start_col+2).Address(), totalDse); err != nil {
			return err
		}

		if err := xl.SetCellValue(DSE_SUMMARY_SHEET, xlutil.Cell(r, dse_entity_col+1).Address(), totalDse); err != nil {
			return err
		}
	}

	return nil
}

func (sdp SdpProcess) WriteSdpReport(xl *excelize.File, data *SdpReport) error {
	startIM3 := xlutil.Cell(10, 2)
	start3ID := xlutil.Cell(30, 2)
	//startIOH := xlutil.Cell(54, 2)

	dIM3 := data.IM3
	d3ID := data.Three
	//dIOH := data.IOH

	// fill asof date
	asofDate := data.AsofDate.Format("02-Jan")
	if err := xl.SetCellValue(SDP_SUMMARY_SHEET, "B8", fmt.Sprintf("Mini Gerai - IM3 as of %s", asofDate)); err != nil {
		return err
	}

	if err := xl.SetCellValue(SDP_SUMMARY_SHEET, "B28", fmt.Sprintf("3-Kiosk as of %s", asofDate)); err != nil {
		return err
	}

	// do IM3
	if err := sdp.writeSdpReport(xl, dIM3, startIM3.Row, startIM3.Row+16); err != nil {
		return err
	}

	// do 3ID
	if err := sdp.writeSdpReport(xl, d3ID, start3ID.Row, start3ID.Row+16); err != nil {
		return err
	}

	// do IOH
	//if err := sdp.writeSdpReport(xl, dIOH, startIOH.Row, startIOH.Row+16); err != nil {
	//	return err
	//}

	return nil
}

func (sdp SdpProcess) writeSdpReport(xl *excelize.File, data *PartnerReportData, startRow, endRow int) error {
	fmList := data.FmList
	sort.Slice(fmList, func(i, j int) bool {
		return fmList[i] > fmList[j]
	})

	for i, mth := range fmList {
		if i > 2 {
			break
		}

		mthDate, err := time.Parse("200601", mth)
		if err != nil {
			return err
		}

		mthName := mthDate.Format("Jan-06")
		if err := xl.SetCellValue(SDP_SUMMARY_SHEET, xlutil.Cell(startRow-1, sdp_ga_start_col+(2-i)).Address(), mthName); err != nil {
			return err
		}

		if err := xl.SetCellValue(SDP_SUMMARY_SHEET, xlutil.Cell(startRow-1, sdp_sec_start_col+(2-i)).Address(), mthName); err != nil {
			return err
		}

	}

	for r := startRow; r <= endRow; r++ {
		entity, err := xl.GetCellValue(SDP_SUMMARY_SHEET, xlutil.Cell(r, sdp_entity_col).Address())
		if err != nil {
			return err
		}
		entity = strings.TrimSpace(entity)
		if entity == "" {
			continue
		}

		if entity == "JAYA" {
			entity = "JAKARTA RAYA"
		}

		var regData *RegionalReportData
		if _, ok := data.CircleData[entity]; ok {
			regData = data.CircleData[entity]
		}

		if _, ok := data.RegionalData[entity]; ok {
			regData = data.RegionalData[entity]
		}

		if entity == "INDONESIA" {
			regData = data.NationalData
		}

		if regData == nil {
			continue
		}

		// fill Full Month data
		for i, mth := range fmList {
			if i > 2 {
				break
			}

			fmData, ok := regData.FM[mth]
			if !ok {
				continue
			}

			gaK := float64(fmData.GrossAdds) / 1000
			gaCol := sdp_ga_start_col + (2 - i)
			sec := fmData.NetSecondaryMn
			secCol := sdp_sec_start_col + (2 - i)

			if err := xl.SetCellValue(SDP_SUMMARY_SHEET, xlutil.Cell(r, gaCol).Address(), gaK); err != nil {
				return err
			}

			if err := xl.SetCellValue(SDP_SUMMARY_SHEET, xlutil.Cell(r, secCol).Address(), sec); err != nil {
				return err
			}
		}

		mtdGaK := float64(regData.MTD.GrossAdds) / 1000
		lmtdGaK := float64(regData.LMTD.GrossAdds) / 1000
		gaAbs := mtdGaK - lmtdGaK
		gaPct := 0.0
		if lmtdGaK != 0 {
			gaPct = float64(mtdGaK-lmtdGaK) / float64(lmtdGaK)
		}

		mtdSec := regData.MTD.NetSecondaryMn
		lmtdSec := regData.LMTD.NetSecondaryMn
		secAbs := mtdSec - lmtdSec
		secPct := (mtdSec - lmtdSec) / lmtdSec

		if err := xl.SetCellValue(SDP_SUMMARY_SHEET, xlutil.Cell(r, sdp_ga_start_col+4).Address(), mtdGaK); err != nil {
			return err
		}

		if err := xl.SetCellValue(SDP_SUMMARY_SHEET, xlutil.Cell(r, sdp_ga_start_col+3).Address(), lmtdGaK); err != nil {
			return err
		}

		if err := xl.SetCellValue(SDP_SUMMARY_SHEET, xlutil.Cell(r, sdp_ga_start_col+5).Address(), gaAbs); err != nil {
			return err
		}

		if err := xl.SetCellValue(SDP_SUMMARY_SHEET, xlutil.Cell(r, sdp_ga_start_col+6).Address(), gaPct); err != nil {
			return err
		}

		if err := xl.SetCellValue(SDP_SUMMARY_SHEET, xlutil.Cell(r, sdp_sec_start_col+4).Address(), mtdSec); err != nil {
			return err
		}

		if err := xl.SetCellValue(SDP_SUMMARY_SHEET, xlutil.Cell(r, sdp_sec_start_col+3).Address(), lmtdSec); err != nil {
			return err
		}

		if err := xl.SetCellValue(SDP_SUMMARY_SHEET, xlutil.Cell(r, sdp_sec_start_col+5).Address(), secAbs); err != nil {
			return err
		}

		if err := xl.SetCellValue(SDP_SUMMARY_SHEET, xlutil.Cell(r, sdp_sec_start_col+6).Address(), secPct); err != nil {
			return err
		}

		// fill GA slab data
		totalDse := 0
		for i, slab := range ga_sdp_labels {
			mtdVal := regData.MTD.GaSlabs[slab]
			totalDse += mtdVal
			if err := xl.SetCellValue(SDP_SUMMARY_SHEET, xlutil.Cell(r, sdp_ga_slab_start_col+i).Address(), mtdVal); err != nil {
				return err
			}
		}
		if err := xl.SetCellValue(SDP_SUMMARY_SHEET, xlutil.Cell(r, sdp_ga_slab_start_col+len(ga_sdp_labels)).Address(), totalDse); err != nil {
			return err
		}

		// fill Secondary slab data
		totalDse = 0
		for i, slab := range sec_sdp_labels {
			mtdVal := regData.MTD.SecondarySlabs[slab]
			totalDse += mtdVal
			if err := xl.SetCellValue(SDP_SUMMARY_SHEET, xlutil.Cell(r, sdp_sec_slab_start_col+i).Address(), mtdVal); err != nil {
				return err
			}
		}
		if err := xl.SetCellValue(SDP_SUMMARY_SHEET, xlutil.Cell(r, sdp_sec_slab_start_col+len(sec_dse_labels)).Address(), totalDse); err != nil {
			return err
		}

	}

	return nil
}

func getDseGaBinLabel(ga int) string {
	for i := 0; i < len(ga_dse_bins)-1; i++ {
		if ga > ga_dse_bins[i] && ga <= ga_dse_bins[i+1] {
			return ga_dse_labels[i]
		}
	}

	return ga_dse_labels[len(ga_dse_labels)-1]
}

func getDseGaDailyBinLabel(gaDaily int) string {
	if gaDaily < 12 {
		return "<12"
	}

	return ">=12"
}

func getDseSecBinLabel(sec float64) string {
	for i := 0; i < len(sec_dse_bins)-1; i++ {
		if sec > float64(sec_dse_bins[i]) && sec <= float64(sec_dse_bins[i+1]) {
			return sec_dse_labels[i]
		}
	}

	return sec_dse_labels[len(sec_dse_labels)-1]
}

func getDseSecDailyBinLabel(secDaily float64) string {
	if secDaily < 5 {
		return "<5"
	}

	return ">=5"
}

func getSdpGaBinLabel(ga int) string {
	for i := 0; i < len(ga_sdp_bins)-1; i++ {
		if ga > ga_sdp_bins[i] && ga <= ga_sdp_bins[i+1] {
			return ga_sdp_labels[i]
		}
	}

	return ga_sdp_labels[len(ga_sdp_labels)-1]
}

func getSdpSecBinLabel(sec float64) string {
	for i := 0; i < len(sec_sdp_bins)-1; i++ {
		if sec > float64(sec_sdp_bins[i]) && sec <= float64(sec_sdp_bins[i+1]) {
			return sec_sdp_labels[i]
		}
	}

	return sec_sdp_labels[len(sec_sdp_labels)-1]
}
