
# ![](./docs/public/logo-xs.png) vue-metamorph

[![NPM License](https://img.shields.io/npm/l/vue-metamorph)](https://github.com/UnrefinedBrain/vue-metamorph/blob/master/LICENSE) [![NPM Version](https://img.shields.io/npm/v/vue-metamorph)](https://npmjs.com/package/vue-metamorph)
 [![GitHub Actions Workflow Status](https://img.shields.io/github/actions/workflow/status/UnrefinedBrain/vue-metamorph/ci.yml)](https://github.com/UnrefinedBrain/vue-metamorph/actions) ![NPM Type Definitions](https://img.shields.io/npm/types/vue-metamorph) [![GitHub Repo stars](https://img.shields.io/github/stars/UnrefinedBrain/vue-metamorph)](https://github.com/UnrefinedBrain/vue-metamorph)

vue-metamorph is a codemod framework for JavaScript, TypeScript, Vue, CSS, SCSS, LESS, and SASS files. It provides an easy way to reliably manipulate source code using abstract syntax trees.

## 📖 Documentation / Installation

[Link to Docs](https://vue-metamorph.dev)

## 🆕 Changelog

See the [GitHub Releases](https://github.com/UnrefinedBrain/vue-metamorph/releases) page.

## 🤝 Contributing

Contributions are welcome! Do you have a use case that vue-metamorph doesn't cover yet? Feel free to file a GitHub issue or open a PR.
