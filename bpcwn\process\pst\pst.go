package pst

import (
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"math"
	"os"
	"path/filepath"
	"slices"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	cfg "github.com/csee-pm/etl/bpcwn/config"

	"io/fs"

	"github.com/csee-pm/etl/shared/channel"
	ctx "github.com/csee-pm/etl/shared/context"
	etlDb "github.com/csee-pm/etl/shared/db"
	etlProc "github.com/csee-pm/etl/shared/process"
	"github.com/csee-pm/etl/shared/utils"
	"github.com/csee-pm/etl/shared/utils/xlutil"
	"github.com/xuri/excelize/v2"
	"gopkg.in/guregu/null.v4"
)

type PstProcess struct {
	procFS fs.ReadFileFS
}

func NewPstProcess(procFS fs.ReadFileFS) PstProcess {
	return PstProcess{procFS}
}

func (pst PstProcess) GetReportData(c context.Context) (PSTReport, error) {
	conf := ctx.ExtractConfig(c)
	workDate := time.Now().AddDate(0, 0, -2)

	var err error
	if conf.Get("etl.mtd_date") != nil {
		mtdDate := conf.GetString("etl.mtd_date")
		workDate, err = time.Parse("20060102", mtdDate)
		if err != nil {
			return PSTReport{}, fmt.Errorf("failed to parse mtd date. %s", err)
		}
	}

	logger := ctx.ExtractLogger(c)
	logger.Debug("work months", "mtd", workDate.Format("20060102"))

	workDir := ctx.ExtractWorkDir(c)

	var wg sync.WaitGroup
	cCancel, cancel := context.WithCancel(c)
	defer cancel()

	var mtdData []MtdPSTData
	var fmData []FmPSTData

	// get MTD data
	mtdResult := channel.RunAsyncContext(cCancel, func() ([]MtdPSTData, error) {
		if cfg.UsePstMtdFromFile != "" {
			return pst.getMtdDataFromFile(cCancel, cfg.UsePstMtdFromFile)
		}
		return pst.getMtdData(cCancel, workDate)
	})

	wg.Add(1)

	go func() {
		defer wg.Done()
		for res := range mtdResult {
			res.Map(func(data []MtdPSTData) {
				mtdData = data
			}).MapErr(func(er error) {
				defer cancel()
				if errors.Is(er, context.Canceled) {
					logger.Debug("canceling get MTD data for PST")
					return
				}
				err = fmt.Errorf("failed to get MTD data for PST. %s", er)
			})
		}
	}()

	// go func() {
	// 	defer wg.Done()
	// 	if cfg.UsePstMtdFromFile != "" {
	// 		mtdData, err = pst.getMtdDataFromFile(cCancel, cfg.UsePstMtdFromFile)
	// 	} else {
	// 		mtdData, err = pst.getMtdData(cCancel, workDate)
	// 	}

	// 	if err != nil {
	// 		logger.Error("failed to get MTD data", "error", err)
	// 		cancel()
	// 	}
	// }()

	fmResult := channel.RunAsyncContext(cCancel, func() ([]FmPSTData, error) {
		return pst.getFmData(cCancel)
	})

	wg.Add(1)

	go func() {
		defer wg.Done()
		for res := range fmResult {
			res.Map(func(data []FmPSTData) {
				fmData = data
			}).MapErr(func(er error) {
				defer cancel()
				if errors.Is(er, context.Canceled) {
					logger.Debug("canceling get FM data for PST")
					return
				}
				err = fmt.Errorf("failed to get FM data for PST. %s", er)
			})
		}
	}()

	// wg.Add(1)
	// go func() {
	// 	defer wg.Done()
	// 	fmData, err = pst.getFmData(cCancel)
	// 	if err != nil {
	// 		logger.Error("failed to get FM data", "error", err)
	// 		cancel()
	// 	}
	// }()

	wg.Wait()
	if err != nil {
		return PSTReport{}, fmt.Errorf("failed to get PST data. %s", err)
	}

	csvFilePath := fmt.Sprintf("%s/pst_mtd_%s.csv", workDir, time.Now().Format("20060102150405"))

	if cfg.UsePstMtdFromFile == "" {
		if err := utils.WriteToCsv(csvFilePath, mtdData); err != nil {
			logger.Error("failed to write CSV file", "path", csvFilePath, "error", err)
		} else {
			logger.Info("CSV file written", "path", csvFilePath)
		}
	}

	//csvMthFilePath := fmt.Sprintf("%s/pst_mth_%s.csv", workDir, time.Now().Format("20060102150405"))
	//err = utils.WriteToCsv(csvMthFilePath, fmData)
	//if err != nil {
	//	logger.Error("failed to write CSV file", "path", csvMthFilePath, "error", err)
	//} else {
	//	logger.Info("CSV file written", "path", csvMthFilePath)
	//}

	reportData, err := pst.postProcessData(mtdData, fmData)
	if err != nil {
		return PSTReport{}, err
	}

	// buf, err := json.MarshalIndent(reportData, "", "  ")
	// if err != nil {
	// 	return nil, err
	// }
	// fmt.Printf("reportData: %+v\n", string(buf))

	return PSTReport{
		MtdDate: workDate,
		IM3: &PSTReportData{
			FmList:      reportData.FmList,
			MTDMonth:    reportData.MTDMonth,
			LMTDMonth:   reportData.LMTDMonth,
			CircleMtd:   reportData.MTD.CircleMtdIm3,
			RegionalMtd: reportData.MTD.RegionalMtdIm3,
			CircleFm:    reportData.Fm.CircleFmIm3,
			RegionalFm:  reportData.Fm.RegionalFmIm3,
		},
		Three: &PSTReportData{
			FmList:      reportData.FmList,
			MTDMonth:    reportData.MTDMonth,
			LMTDMonth:   reportData.LMTDMonth,
			CircleMtd:   reportData.MTD.CircleMtd3id,
			RegionalMtd: reportData.MTD.RegionalMtd3id,
			CircleFm:    reportData.Fm.CircleFm3id,
			RegionalFm:  reportData.Fm.RegionalFm3id,
		},
		IOH: &PSTReportData{
			FmList:      reportData.FmList,
			MTDMonth:    reportData.MTDMonth,
			LMTDMonth:   reportData.LMTDMonth,
			CircleMtd:   reportData.MTD.CircleMtdIOH,
			RegionalMtd: reportData.MTD.RegionalMtdIOH,
			CircleFm:    reportData.Fm.CircleFmIOH,
			RegionalFm:  reportData.Fm.RegionalFmIOH,
		},
	}, nil
}

func (pst PstProcess) getMtdData(c context.Context, mtdDate time.Time) ([]MtdPSTData, error) {
	logger := ctx.ExtractLogger(c)

	buf, err := pst.procFS.ReadFile("files/cwn_pst_mtd.sql")
	if err != nil {
		return nil, err
	}

	params := map[string]*etlDb.ParamValue{
		"mtd_dt_id": {Name: "mtd_dt_id", Value: mtdDate.Format(utils.ToGoDateFormat("YYYYMMDD"))},
	}

	logger.Info("Getting MTD data")
	return etlProc.QueryImpalaData[MtdPSTData](c, string(buf), params)
}

func (pst PstProcess) getMtdDataFromFile(c context.Context, filePath string) ([]MtdPSTData, error) {
	logger := ctx.ExtractLogger(c)

	f, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	logger.Info("Getting MTD data from CSV file", "path", filePath)

	cr := csv.NewReader(f)
	cr.Comma = ','
	cr.LazyQuotes = true

	cr.Read()

	var data []MtdPSTData
	for {
		record, err := cr.Read()
		if err == io.EOF {
			break
		}

		if err != nil {
			return nil, err
		}

		primaryVal := null.NewFloat(0, false)
		primary, err := strconv.ParseFloat(record[6], 64)
		if err == nil {
			primaryVal = null.FloatFrom(primary)
		}

		secondaryVal := null.NewFloat(0, false)
		secondary, err := strconv.ParseFloat(record[7], 64)
		if err == nil {
			secondaryVal = null.FloatFrom(secondary)
		}

		tertiaryVal := null.NewFloat(0, false)
		tertiary, err := strconv.ParseFloat(record[8], 64)
		if err == nil {
			tertiaryVal = null.FloatFrom(tertiary)
		}

		data = append(data, MtdPSTData{
			MonthID:  record[0],
			Period:   record[1],
			AsofDate: record[2],
			RegionPSTKpi: RegionPSTKpi{
				Circle: null.StringFrom(record[3]),
				Region: null.StringFrom(record[4]),
				Brand:  record[5],
				PSTKpi: PSTKpi{
					Primary:   primaryVal,
					Secondary: secondaryVal,
					Tertiary:  tertiaryVal,
				},
			},
		})
	}

	return data, nil
}

func (pst PstProcess) getFmData(c context.Context) ([]FmPSTData, error) {
	logger := ctx.ExtractLogger(c)
	rootdir := ctx.ExtractRootDir(c)
	fmCsv := filepath.Join(rootdir, "PST_FM.csv")

	var data []FmPSTData

	f, err := os.Open(fmCsv)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	logger.Info("Getting PST FullMonth data from CSV file", "path", fmCsv)

	cr := csv.NewReader(f)
	cr.Comma = ','
	cr.LazyQuotes = true

	cr.Read()
	for {
		record, err := cr.Read()
		if err == io.EOF {
			break
		}

		if err != nil {
			return nil, err
		}

		primary, err := strconv.ParseFloat(record[4], 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse primary amount. %s", err)
		}

		secondary, err := strconv.ParseFloat(record[5], 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse secondary amount. %s", err)
		}

		tertiary, err := strconv.ParseFloat(record[6], 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse tertiary amount. %s", err)
		}

		data = append(data, FmPSTData{
			MonthID: record[0],
			RegionPSTKpi: RegionPSTKpi{
				Circle: null.StringFrom(record[1]),
				Region: null.StringFrom(record[2]),
				Brand:  record[3],
				PSTKpi: PSTKpi{
					Primary:   null.FloatFrom(primary),
					Secondary: null.FloatFrom(secondary),
					Tertiary:  null.FloatFrom(tertiary),
				},
			},
		})
	}

	return data, nil
}

func (pst PstProcess) postProcessData(mtd []MtdPSTData, fm []FmPSTData) (ProcessedPSTReportData, error) {
	mtdData, err := pst.postProcessMtdData(mtd)
	if err != nil {
		return ProcessedPSTReportData{}, err
	}

	fmData, err := pst.postProcessFmData(fm)
	if err != nil {
		return ProcessedPSTReportData{}, err
	}

	fmMonthMap := make(map[string]struct{})
	mapProcessor := func(key string, val map[string]*FmPSTData) {
		for mkey := range val {
			fmMonthMap[mkey] = struct{}{}
		}
	}

	utils.MapProcessor(fmData.CircleFm3id, mapProcessor)
	utils.MapProcessor(fmData.RegionalFm3id, mapProcessor)
	utils.MapProcessor(fmData.CircleFmIm3, mapProcessor)
	utils.MapProcessor(fmData.RegionalFmIm3, mapProcessor)

	fmList := utils.MapToList(fmMonthMap, func(key string, value struct{}) string {
		return key
	})

	sort.Strings(fmList)

	return ProcessedPSTReportData{
		FmList: fmList,
		MTD:    mtdData,
		Fm:     fmData,
	}, nil
}

func (pst PstProcess) postProcessMtdData(data []MtdPSTData) (MtdPSTReportData, error) {
	regMapIm3 := make(map[string]*MtdPSTKpi)
	regMap3id := make(map[string]*MtdPSTKpi)
	regMapIOH := make(map[string]*MtdPSTKpi)
	cirMapIm3 := make(map[string]*MtdPSTKpi)
	cirMap3id := make(map[string]*MtdPSTKpi)
	cirMapIOH := make(map[string]*MtdPSTKpi)

	for i := range data {
		mtd := data[i]
		reg := mtd.Region.String
		cir := mtd.Circle.String
		brand := mtd.Brand

		var regMap = regMapIm3
		var cirMap = cirMapIm3

		if brand == "3ID" {
			regMap = regMap3id
			cirMap = cirMap3id
		}

		if _, ok := regMap[reg]; !ok {
			regMap[reg] = &MtdPSTKpi{}
		}

		if _, ok := regMapIOH[reg]; !ok {
			regMapIOH[reg] = &MtdPSTKpi{
				MTD: MtdPSTData{RegionPSTKpi: RegionPSTKpi{
					Circle: mtd.Circle,
					Region: mtd.Region,
					Brand:  "IOH",
					PSTKpi: PSTKpi{
						Primary:   null.FloatFrom(0),
						Secondary: null.FloatFrom(0),
						Tertiary:  null.FloatFrom(0),
					},
				}},
				LMTD: MtdPSTData{RegionPSTKpi: RegionPSTKpi{
					Circle: mtd.Circle,
					Region: mtd.Region,
					Brand:  "IOH",
					PSTKpi: PSTKpi{
						Primary:   null.FloatFrom(0),
						Secondary: null.FloatFrom(0),
						Tertiary:  null.FloatFrom(0),
					},
				}},
			}
		}

		if _, ok := cirMap[cir]; !ok {
			cirMap[cir] = &MtdPSTKpi{
				MTD: MtdPSTData{RegionPSTKpi: RegionPSTKpi{
					Circle: mtd.Circle,
					Brand:  mtd.Brand,
					PSTKpi: PSTKpi{
						Primary:   null.FloatFrom(0),
						Secondary: null.FloatFrom(0),
						Tertiary:  null.FloatFrom(0),
					},
				}},
				LMTD: MtdPSTData{RegionPSTKpi: RegionPSTKpi{
					Circle: mtd.Circle,
					Brand:  mtd.Brand,
					PSTKpi: PSTKpi{
						Primary:   null.FloatFrom(0),
						Secondary: null.FloatFrom(0),
						Tertiary:  null.FloatFrom(0),
					},
				}},
			}
		}

		if _, ok := cirMapIOH[cir]; !ok {
			cirMapIOH[cir] = &MtdPSTKpi{
				MTD: MtdPSTData{RegionPSTKpi: RegionPSTKpi{
					Circle: mtd.Circle,
					Brand:  "IOH",
					PSTKpi: PSTKpi{
						Primary:   null.FloatFrom(0),
						Secondary: null.FloatFrom(0),
						Tertiary:  null.FloatFrom(0),
					},
				}},
				LMTD: MtdPSTData{RegionPSTKpi: RegionPSTKpi{
					Circle: mtd.Circle,
					Brand:  "IOH",
					PSTKpi: PSTKpi{
						Primary:   null.FloatFrom(0),
						Secondary: null.FloatFrom(0),
						Tertiary:  null.FloatFrom(0),
					},
				}},
			}
		}

		if strings.ToUpper(mtd.Period) == "MTD" {
			regMap[reg].MTD = MtdPSTData{
				MonthID:  mtd.MonthID,
				Period:   mtd.Period,
				AsofDate: mtd.AsofDate,
				RegionPSTKpi: RegionPSTKpi{
					Circle: mtd.Circle,
					Region: mtd.Region,
					Brand:  mtd.Brand,
					PSTKpi: PSTKpi{
						Primary:   mtd.Primary,
						Secondary: mtd.Secondary,
						Tertiary:  mtd.Tertiary,
					},
				},
			}

			regMapIOH[reg].MTD.MonthID = mtd.MonthID
			regMapIOH[reg].MTD.Period = mtd.Period
			regMapIOH[reg].MTD.AsofDate = mtd.AsofDate
			regMapIOH[reg].MTD.RegionPSTKpi.Primary.Float64 += mtd.Primary.Float64
			regMapIOH[reg].MTD.RegionPSTKpi.Secondary.Float64 += mtd.Secondary.Float64
			regMapIOH[reg].MTD.RegionPSTKpi.Tertiary.Float64 += mtd.Tertiary.Float64

			cirMap[cir].MTD.MonthID = mtd.MonthID
			cirMap[cir].MTD.Period = mtd.Period
			cirMap[cir].MTD.AsofDate = mtd.AsofDate
			cirMap[cir].MTD.RegionPSTKpi.Primary.Float64 += mtd.Primary.Float64
			cirMap[cir].MTD.RegionPSTKpi.Secondary.Float64 += mtd.Secondary.Float64
			cirMap[cir].MTD.RegionPSTKpi.Tertiary.Float64 += mtd.Tertiary.Float64

			cirMapIOH[cir].MTD.MonthID = mtd.MonthID
			cirMapIOH[cir].MTD.Period = mtd.Period
			cirMapIOH[cir].MTD.AsofDate = mtd.AsofDate
			cirMapIOH[cir].MTD.RegionPSTKpi.Primary.Float64 += mtd.Primary.Float64
			cirMapIOH[cir].MTD.RegionPSTKpi.Secondary.Float64 += mtd.Secondary.Float64
			cirMapIOH[cir].MTD.RegionPSTKpi.Tertiary.Float64 += mtd.Tertiary.Float64
		}

		if strings.ToUpper(mtd.Period) == "LMTD" {
			regMap[reg].LMTD = MtdPSTData{
				MonthID:  mtd.MonthID,
				Period:   mtd.Period,
				AsofDate: mtd.AsofDate,
				RegionPSTKpi: RegionPSTKpi{
					Circle: mtd.Circle,
					Region: mtd.Region,
					Brand:  mtd.Brand,
					PSTKpi: PSTKpi{
						Primary:   mtd.Primary,
						Secondary: mtd.Secondary,
						Tertiary:  mtd.Tertiary,
					},
				},
			}

			regMapIOH[reg].LMTD.MonthID = mtd.MonthID
			regMapIOH[reg].LMTD.Period = mtd.Period
			regMapIOH[reg].LMTD.AsofDate = mtd.AsofDate
			regMapIOH[reg].LMTD.RegionPSTKpi.Primary.Float64 += mtd.Primary.Float64
			regMapIOH[reg].LMTD.RegionPSTKpi.Secondary.Float64 += mtd.Secondary.Float64
			regMapIOH[reg].LMTD.RegionPSTKpi.Tertiary.Float64 += mtd.Tertiary.Float64

			cirMap[cir].LMTD.MonthID = mtd.MonthID
			cirMap[cir].LMTD.Period = mtd.Period
			cirMap[cir].LMTD.AsofDate = mtd.AsofDate
			cirMap[cir].LMTD.RegionPSTKpi.Primary.Float64 += mtd.Primary.Float64
			cirMap[cir].LMTD.RegionPSTKpi.Secondary.Float64 += mtd.Secondary.Float64
			cirMap[cir].LMTD.RegionPSTKpi.Tertiary.Float64 += mtd.Tertiary.Float64

			cirMapIOH[cir].LMTD.MonthID = mtd.MonthID
			cirMapIOH[cir].LMTD.Period = mtd.Period
			cirMapIOH[cir].LMTD.AsofDate = mtd.AsofDate
			cirMapIOH[cir].LMTD.RegionPSTKpi.Primary.Float64 += mtd.Primary.Float64
			cirMapIOH[cir].LMTD.RegionPSTKpi.Secondary.Float64 += mtd.Secondary.Float64
			cirMapIOH[cir].LMTD.RegionPSTKpi.Tertiary.Float64 += mtd.Tertiary.Float64
		}
	}

	return MtdPSTReportData{
		CircleMtdIm3:   cirMapIm3,
		RegionalMtdIm3: regMapIm3,
		CircleMtd3id:   cirMap3id,
		RegionalMtd3id: regMap3id,
		CircleMtdIOH:   cirMapIOH,
		RegionalMtdIOH: regMapIOH,
	}, nil
}

func (pst PstProcess) postProcessFmData(data []FmPSTData) (FmPSTReportData, error) {
	regMapIm3 := make(map[string]map[string]*FmPSTData)
	regMap3id := make(map[string]map[string]*FmPSTData)
	regMapIOH := make(map[string]map[string]*FmPSTData)
	cirMapIm3 := make(map[string]map[string]*FmPSTData)
	cirMap3id := make(map[string]map[string]*FmPSTData)
	cirMapIOH := make(map[string]map[string]*FmPSTData)

	for i := range data {
		reg := data[i].Region.String
		cir := data[i].Circle.String
		fmData := data[i]
		monthID := fmData.MonthID
		brand := fmData.Brand

		regMap := regMapIm3
		cirMap := cirMapIm3

		if brand == "3ID" {
			regMap = regMap3id
			cirMap = cirMap3id
		}

		if _, ok := regMap[reg]; !ok {
			regMap[reg] = make(map[string]*FmPSTData)
		}

		if _, ok := regMapIOH[reg]; !ok {
			regMapIOH[reg] = make(map[string]*FmPSTData)
		}

		if _, ok := cirMap[cir]; !ok {
			cirMap[cir] = make(map[string]*FmPSTData)
		}

		if _, ok := cirMapIOH[cir]; !ok {
			cirMapIOH[cir] = make(map[string]*FmPSTData)
		}

		regMap[reg][monthID] = &fmData

		if _, ok := regMapIOH[reg][monthID]; !ok {
			regMapIOH[reg][monthID] = &FmPSTData{
				MonthID: monthID,
				RegionPSTKpi: RegionPSTKpi{
					Circle: fmData.Circle,
					Region: fmData.Region,
					Brand:  "IOH",
					PSTKpi: PSTKpi{
						Primary:   null.FloatFrom(0),
						Secondary: null.FloatFrom(0),
						Tertiary:  null.FloatFrom(0),
					},
				},
			}
		}

		regMapIOH[reg][monthID].Primary.Float64 += fmData.Primary.Float64
		regMapIOH[reg][monthID].Secondary.Float64 += fmData.Secondary.Float64
		regMapIOH[reg][monthID].Tertiary.Float64 += fmData.Tertiary.Float64

		if _, ok := cirMap[cir][monthID]; !ok {
			cirMap[cir][monthID] = &FmPSTData{
				MonthID: monthID,
				RegionPSTKpi: RegionPSTKpi{
					Circle: fmData.Circle,
					Brand:  fmData.Brand,
					PSTKpi: PSTKpi{
						Primary:   null.FloatFrom(0),
						Secondary: null.FloatFrom(0),
						Tertiary:  null.FloatFrom(0),
					},
				},
			}
		}

		cirMap[cir][monthID].Primary.Float64 += fmData.Primary.Float64
		cirMap[cir][monthID].Secondary.Float64 += fmData.Secondary.Float64
		cirMap[cir][monthID].Tertiary.Float64 += fmData.Tertiary.Float64

		if _, ok := cirMapIOH[cir][monthID]; !ok {
			cirMapIOH[cir][monthID] = &FmPSTData{
				MonthID: monthID,
				RegionPSTKpi: RegionPSTKpi{
					Circle: fmData.Circle,
					Brand:  "IOH",
					PSTKpi: PSTKpi{
						Primary:   null.FloatFrom(0),
						Secondary: null.FloatFrom(0),
						Tertiary:  null.FloatFrom(0),
					},
				},
			}
		}

		cirMapIOH[cir][monthID].Primary.Float64 += fmData.Primary.Float64
		cirMapIOH[cir][monthID].Secondary.Float64 += fmData.Secondary.Float64
		cirMapIOH[cir][monthID].Tertiary.Float64 += fmData.Tertiary.Float64
	}

	return FmPSTReportData{
		CircleFmIm3:   cirMapIm3,
		RegionalFmIm3: regMapIm3,
		CircleFm3id:   cirMap3id,
		RegionalFm3id: regMap3id,
		CircleFmIOH:   cirMapIOH,
		RegionalFmIOH: regMapIOH,
	}, nil
}

var (
	PSTPrimaryStartCol     = 4
	PSTSecondaryStartCol   = 12
	PSTTertiaryStartCol    = 20
	PSTSecTerRatioStartCol = 28

	PSTPrimaryMtdCol    = 8
	PSTPrimaryLMtdCol   = 7
	PSTPrimaryMtdAbsCol = 9
	PSTPrimaryMtdGrowth = 10
	PSTPrimaryFmCols    = []int{4, 5, 6}

	PSTSecondaryMtdCol    = 16
	PSTSecondaryLMtdCol   = 15
	PSTSecondaryMtdAbsCol = 17
	PSTSecondaryMtdGrowth = 18
	PSTSecondaryFmCols    = []int{12, 13, 14}

	PSTTertiaryMtdCol    = 24
	PSTTertiaryLMtdCol   = 23
	PSTTertiaryMtdAbsCol = 25
	PSTTertiaryMtdGrowth = 26
	PSTTertiaryFmCols    = []int{20, 21, 22}

	PSTSecTerRatioMtdCol  = 32
	PSTSecTerRatioLMtdCol = 31
	PSTSecTerRatioFmCols  = []int{28, 29, 30}

	PSTDataStartCol = 4

	mtdColOffset    = 4
	lmtdColOffset   = 3
	absColOffset    = 5
	growthColOffset = 6
)

func (pst PstProcess) getPSTReportSheet() string {
	return "Summary PST by Region"
}

func (pst PstProcess) WriteReport(xl *excelize.File, data *PSTReport) error {
	startIM3 := xlutil.Cell(32, PSTDataStartCol)
	start3ID := xlutil.Cell(54, PSTDataStartCol)
	startIOH := xlutil.Cell(10, PSTDataStartCol)

	dIM3 := data.IM3
	d3ID := data.Three
	dIOH := data.IOH

	//for key, mtd := range d3ID.RegionalMtd {
	//	fmt.Printf("%s: %+v\n", key, mtd.MTD)
	//}

	shName := pst.getPSTReportSheet()
	asofDate := data.MtdDate.Format("AsOf: 02-Jan-2006")
	xl.SetCellValue(shName, xlutil.Cell(5, 2).Address(), asofDate)

	// do IM3
	if err := pst.writeReport(xl, dIM3, startIM3.Row, startIM3.Col); err != nil {
		return err
	}

	// do 3ID
	if err := pst.writeReport(xl, d3ID, start3ID.Row, start3ID.Col); err != nil {
		return err
	}

	// do IOH
	if err := pst.writeReport(xl, dIOH, startIOH.Row, startIOH.Col); err != nil {
		return err
	}

	return nil
}

func (pst PstProcess) writeReport(xl *excelize.File, data *PSTReportData, startRow, startCol int) error {
	shName := pst.getPSTReportSheet()

	nullMtd, ok := data.CircleMtd[""]
	if !ok {
		nullMtd = &MtdPSTKpi{
			MTD:  MtdPSTData{RegionPSTKpi: RegionPSTKpi{PSTKpi: PSTKpi{Primary: null.FloatFrom(0), Secondary: null.FloatFrom(0), Tertiary: null.FloatFrom(0)}}},
			LMTD: MtdPSTData{RegionPSTKpi: RegionPSTKpi{PSTKpi: PSTKpi{Primary: null.FloatFrom(0), Secondary: null.FloatFrom(0), Tertiary: null.FloatFrom(0)}}},
		}
	}

	mtdTotal := PSTKpi{
		Primary:   null.FloatFrom(nullMtd.MTD.Primary.Float64),
		Secondary: null.FloatFrom(nullMtd.MTD.Secondary.Float64),
		Tertiary:  null.FloatFrom(nullMtd.MTD.Tertiary.Float64),
	}

	lmtdTotal := PSTKpi{
		Primary:   null.FloatFrom(nullMtd.LMTD.Primary.Float64),
		Secondary: null.FloatFrom(nullMtd.LMTD.Secondary.Float64),
		Tertiary:  null.FloatFrom(nullMtd.LMTD.Tertiary.Float64),
	}

	fmList := data.FmList

	slices.SortFunc(fmList, func(a, b string) int {
		return strings.Compare(b, a)
	})

	if len(fmList) > 3 {
		fmList = fmList[:3]
	}

	for i, mth := range fmList {
		if i > 2 {
			break
		}

		mthDate, err := time.Parse("200601", mth)
		if err != nil {
			return err
		}

		mthName := mthDate.Format("Jan-06")
		if err := xl.SetCellValue(shName, xlutil.Cell(startRow-1, PSTPrimaryStartCol+(2-i)).Address(), mthName); err != nil {
			return err
		}
		if err := xl.SetCellValue(shName, xlutil.Cell(startRow-1, PSTSecondaryStartCol+(2-i)).Address(), mthName); err != nil {
			return err
		}
		if err := xl.SetCellValue(shName, xlutil.Cell(startRow-1, PSTTertiaryStartCol+(2-i)).Address(), mthName); err != nil {
			return err
		}
		if err := xl.SetCellValue(shName, xlutil.Cell(startRow-1, PSTSecTerRatioStartCol+(2-i)).Address(), mthName); err != nil {
			return err
		}
	}

	fmTotal := make(map[string]*PSTKpi)
	nullFm, ok := data.CircleFm[""]
	if !ok {
		nullFm = make(map[string]*FmPSTData)
		for _, mth := range fmList {
			nullFm[mth] = &FmPSTData{
				RegionPSTKpi: RegionPSTKpi{
					PSTKpi: PSTKpi{
						Primary:   null.FloatFrom(0),
						Secondary: null.FloatFrom(0),
						Tertiary:  null.FloatFrom(0),
					},
				},
			}
		}
	}

	for _, mth := range fmList {
		if nullData, ok := nullFm[mth]; ok {
			fmTotal[mth] = &PSTKpi{
				Primary:   null.FloatFrom(nullData.Primary.Float64),
				Secondary: null.FloatFrom(nullData.Secondary.Float64),
				Tertiary:  null.FloatFrom(nullData.Tertiary.Float64),
			}
		}
	}

	for i := 0; i < 17; i++ {
		r := startRow + i
		entity, err := xl.GetCellValue(shName, xlutil.Cell(r, startCol-2).Address())
		if err != nil {
			return err
		}
		entity = strings.TrimSpace(entity)

		if entity == "" {
			continue
		}

		if entity == "JAYA" {
			entity = "JAKARTA RAYA"
		}

		var mtd *MtdPSTKpi
		if _, ok := data.CircleMtd[entity]; ok {
			mtd = data.CircleMtd[entity]
			mtdTotal.Primary.Float64 += mtd.MTD.Primary.Float64
			mtdTotal.Secondary.Float64 += mtd.MTD.Secondary.Float64
			mtdTotal.Tertiary.Float64 += mtd.MTD.Tertiary.Float64

			lmtdTotal.Primary.Float64 += mtd.LMTD.Primary.Float64
			lmtdTotal.Secondary.Float64 += mtd.LMTD.Secondary.Float64
			lmtdTotal.Tertiary.Float64 += mtd.LMTD.Tertiary.Float64
		}

		if _, ok := data.RegionalMtd[entity]; ok {
			mtd = data.RegionalMtd[entity]
		}

		if mtd != nil {
			// Primary
			xl.SetCellValue(shName, xlutil.Cell(r, PSTPrimaryStartCol+mtdColOffset).Address(), mtd.MTD.Primary.Float64/math.Pow10(9))
			xl.SetCellValue(shName, xlutil.Cell(r, PSTPrimaryStartCol+lmtdColOffset).Address(), mtd.LMTD.Primary.Float64/math.Pow10(9))
			primaryAbs := mtd.MTD.Primary.Float64 - mtd.LMTD.Primary.Float64
			xl.SetCellValue(shName, xlutil.Cell(r, PSTPrimaryStartCol+absColOffset).Address(), primaryAbs/math.Pow10(9))
			xl.SetCellValue(shName, xlutil.Cell(r, PSTPrimaryStartCol+growthColOffset).Address(), (mtd.MTD.Primary.Float64-mtd.LMTD.Primary.Float64)/mtd.LMTD.Primary.Float64)

			//Secondary
			xl.SetCellValue(shName, xlutil.Cell(r, PSTSecondaryStartCol+mtdColOffset).Address(), mtd.MTD.Secondary.Float64/math.Pow10(9))
			xl.SetCellValue(shName, xlutil.Cell(r, PSTSecondaryStartCol+lmtdColOffset).Address(), mtd.LMTD.Secondary.Float64/math.Pow10(9))
			secondaryAbs := mtd.MTD.Secondary.Float64 - mtd.LMTD.Secondary.Float64
			xl.SetCellValue(shName, xlutil.Cell(r, PSTSecondaryStartCol+absColOffset).Address(), secondaryAbs/math.Pow10(9))
			xl.SetCellValue(shName, xlutil.Cell(r, PSTSecondaryStartCol+growthColOffset).Address(), (mtd.MTD.Secondary.Float64-mtd.LMTD.Secondary.Float64)/mtd.LMTD.Secondary.Float64)

			//Tertiary
			xl.SetCellValue(shName, xlutil.Cell(r, PSTTertiaryStartCol+mtdColOffset).Address(), mtd.MTD.Tertiary.Float64/math.Pow10(9))
			xl.SetCellValue(shName, xlutil.Cell(r, PSTTertiaryStartCol+lmtdColOffset).Address(), mtd.LMTD.Tertiary.Float64/math.Pow10(9))
			tertiaryAbs := mtd.MTD.Tertiary.Float64 - mtd.LMTD.Tertiary.Float64
			xl.SetCellValue(shName, xlutil.Cell(r, PSTTertiaryStartCol+absColOffset).Address(), tertiaryAbs/math.Pow10(9))
			xl.SetCellValue(shName, xlutil.Cell(r, PSTTertiaryStartCol+growthColOffset).Address(), (mtd.MTD.Tertiary.Float64-mtd.LMTD.Tertiary.Float64)/mtd.LMTD.Tertiary.Float64)

			// Sec/Ter ratio
			xl.SetCellValue(shName, xlutil.Cell(r, PSTSecTerRatioStartCol+mtdColOffset).Address(), mtd.MTD.Secondary.Float64/mtd.MTD.Tertiary.Float64)
			xl.SetCellValue(shName, xlutil.Cell(r, PSTSecTerRatioStartCol+lmtdColOffset).Address(), mtd.LMTD.Secondary.Float64/mtd.LMTD.Tertiary.Float64)
		}

		var fm map[string]*FmPSTData
		if _, ok := data.CircleFm[entity]; ok {
			fm = data.CircleFm[entity]
			for _, mth := range fmList {
				fmTotal[mth].Primary.Float64 += fm[mth].Primary.Float64
				fmTotal[mth].Secondary.Float64 += fm[mth].Secondary.Float64
				fmTotal[mth].Tertiary.Float64 += fm[mth].Tertiary.Float64
			}
		}

		if _, ok := data.RegionalFm[entity]; ok {
			fm = data.RegionalFm[entity]
		}

		if fm != nil {
			for m, mth := range fmList {
				if m > 2 {
					break
				}

				//Primary
				xl.SetCellValue(shName, xlutil.Cell(r, PSTPrimaryStartCol+(2-m)).Address(), fm[mth].Primary.Float64)

				//Secondary
				xl.SetCellValue(shName, xlutil.Cell(r, PSTSecondaryStartCol+(2-m)).Address(), fm[mth].Secondary.Float64)

				//Tertiary
				xl.SetCellValue(shName, xlutil.Cell(r, PSTTertiaryStartCol+(2-m)).Address(), fm[mth].Tertiary.Float64)

				//Sec/Ter ratio
				xl.SetCellValue(shName, xlutil.Cell(r, PSTSecTerRatioStartCol+(2-m)).Address(), fm[mth].Secondary.Float64/fm[mth].Tertiary.Float64)
			}
		}

		if entity == "INDONESIA" {
			// Primary
			xl.SetCellValue(shName, xlutil.Cell(r, PSTPrimaryStartCol+mtdColOffset).Address(), mtdTotal.Primary.Float64/math.Pow10(9))
			xl.SetCellValue(shName, xlutil.Cell(r, PSTPrimaryStartCol+lmtdColOffset).Address(), lmtdTotal.Primary.Float64/math.Pow10(9))
			primaryAbs := mtdTotal.Primary.Float64 - lmtdTotal.Primary.Float64
			xl.SetCellValue(shName, xlutil.Cell(r, PSTPrimaryStartCol+absColOffset).Address(), primaryAbs/math.Pow10(9))
			xl.SetCellValue(shName, xlutil.Cell(r, PSTPrimaryStartCol+growthColOffset).Address(), (mtdTotal.Primary.Float64-lmtdTotal.Primary.Float64)/lmtdTotal.Primary.Float64)

			// Secondary
			xl.SetCellValue(shName, xlutil.Cell(r, PSTSecondaryStartCol+mtdColOffset).Address(), mtdTotal.Secondary.Float64/math.Pow10(9))
			xl.SetCellValue(shName, xlutil.Cell(r, PSTSecondaryStartCol+lmtdColOffset).Address(), lmtdTotal.Secondary.Float64/math.Pow10(9))
			secondaryAbs := mtdTotal.Secondary.Float64 - lmtdTotal.Secondary.Float64
			xl.SetCellValue(shName, xlutil.Cell(r, PSTSecondaryStartCol+absColOffset).Address(), secondaryAbs/math.Pow10(9))
			xl.SetCellValue(shName, xlutil.Cell(r, PSTSecondaryStartCol+growthColOffset).Address(), (mtdTotal.Secondary.Float64-lmtdTotal.Secondary.Float64)/lmtdTotal.Secondary.Float64)

			// Tertiary
			xl.SetCellValue(shName, xlutil.Cell(r, PSTTertiaryStartCol+mtdColOffset).Address(), mtdTotal.Tertiary.Float64/math.Pow10(9))
			xl.SetCellValue(shName, xlutil.Cell(r, PSTTertiaryStartCol+lmtdColOffset).Address(), lmtdTotal.Tertiary.Float64/math.Pow10(9))
			tertiaryAbs := mtdTotal.Tertiary.Float64 - lmtdTotal.Tertiary.Float64
			xl.SetCellValue(shName, xlutil.Cell(r, PSTTertiaryStartCol+absColOffset).Address(), tertiaryAbs/math.Pow10(9))
			xl.SetCellValue(shName, xlutil.Cell(r, PSTTertiaryStartCol+growthColOffset).Address(), (mtdTotal.Tertiary.Float64-lmtdTotal.Tertiary.Float64)/lmtdTotal.Tertiary.Float64)

			// Sec/Ter ratio
			xl.SetCellValue(shName, xlutil.Cell(r, PSTSecTerRatioStartCol+mtdColOffset).Address(), mtdTotal.Secondary.Float64/mtdTotal.Tertiary.Float64)
			xl.SetCellValue(shName, xlutil.Cell(r, PSTSecTerRatioStartCol+lmtdColOffset).Address(), lmtdTotal.Secondary.Float64/lmtdTotal.Tertiary.Float64)

			for m, mth := range fmList {
				if m > 2 {
					break
				}

				xl.SetCellValue(shName, xlutil.Cell(r, PSTPrimaryStartCol+(2-m)).Address(), fmTotal[mth].Primary.Float64)

				xl.SetCellValue(shName, xlutil.Cell(r, PSTSecondaryStartCol+(2-m)).Address(), fmTotal[mth].Secondary.Float64)

				xl.SetCellValue(shName, xlutil.Cell(r, PSTTertiaryStartCol+(2-m)).Address(), fmTotal[mth].Tertiary.Float64)

				xl.SetCellValue(shName, xlutil.Cell(r, PSTSecTerRatioStartCol+(2-m)).Address(), fmTotal[mth].Secondary.Float64/fmTotal[mth].Tertiary.Float64)
			}
		}
	}

	return nil
}
