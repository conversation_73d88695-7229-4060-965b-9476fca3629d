#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/@unovue+detypes@0.8.5/node_modules/@unovue/detypes/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/@unovue+detypes@0.8.5/node_modules/@unovue/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/@unovue+detypes@0.8.5/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/@unovue+detypes@0.8.5/node_modules/@unovue/detypes/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/@unovue+detypes@0.8.5/node_modules/@unovue/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/@unovue+detypes@0.8.5/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../@unovue+detypes@0.8.5/node_modules/@unovue/detypes/detype.js" "$@"
else
  exec node  "$basedir/../../../../../@unovue+detypes@0.8.5/node_modules/@unovue/detypes/detype.js" "$@"
fi
