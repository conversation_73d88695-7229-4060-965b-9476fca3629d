#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/shadcn-vue@2.1.0_@vitest+ui@3.1.4_eslint@9.27.0_jiti@2.4.2__typescript@5.8.3_vitest@3.1.4_vue@3.5.14_typescript@5.8.3_/node_modules/shadcn-vue/dist/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/shadcn-vue@2.1.0_@vitest+ui@3.1.4_eslint@9.27.0_jiti@2.4.2__typescript@5.8.3_vitest@3.1.4_vue@3.5.14_typescript@5.8.3_/node_modules/shadcn-vue/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/shadcn-vue@2.1.0_@vitest+ui@3.1.4_eslint@9.27.0_jiti@2.4.2__typescript@5.8.3_vitest@3.1.4_vue@3.5.14_typescript@5.8.3_/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/shadcn-vue@2.1.0_@vitest+ui@3.1.4_eslint@9.27.0_jiti@2.4.2__typescript@5.8.3_vitest@3.1.4_vue@3.5.14_typescript@5.8.3_/node_modules/shadcn-vue/dist/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/shadcn-vue@2.1.0_@vitest+ui@3.1.4_eslint@9.27.0_jiti@2.4.2__typescript@5.8.3_vitest@3.1.4_vue@3.5.14_typescript@5.8.3_/node_modules/shadcn-vue/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/shadcn-vue@2.1.0_@vitest+ui@3.1.4_eslint@9.27.0_jiti@2.4.2__typescript@5.8.3_vitest@3.1.4_vue@3.5.14_typescript@5.8.3_/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../dist/index.js" "$@"
else
  exec node  "$basedir/../../dist/index.js" "$@"
fi
