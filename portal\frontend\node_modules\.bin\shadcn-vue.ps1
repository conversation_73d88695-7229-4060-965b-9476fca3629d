#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="C:\Users\<USER>\Projects\go\csee\etl\portal\frontend\node_modules\.pnpm\shadcn-vue@2.1.0_@vitest+ui@3.1.4_eslint@9.27.0_jiti@2.4.2__typescript@5.8.3_vitest@3.1.4_vue@3.5.14_typescript@5.8.3_\node_modules\shadcn-vue\dist\node_modules;C:\Users\<USER>\Projects\go\csee\etl\portal\frontend\node_modules\.pnpm\shadcn-vue@2.1.0_@vitest+ui@3.1.4_eslint@9.27.0_jiti@2.4.2__typescript@5.8.3_vitest@3.1.4_vue@3.5.14_typescript@5.8.3_\node_modules\shadcn-vue\node_modules;C:\Users\<USER>\Projects\go\csee\etl\portal\frontend\node_modules\.pnpm\shadcn-vue@2.1.0_@vitest+ui@3.1.4_eslint@9.27.0_jiti@2.4.2__typescript@5.8.3_vitest@3.1.4_vue@3.5.14_typescript@5.8.3_\node_modules;C:\Users\<USER>\Projects\go\csee\etl\portal\frontend\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/shadcn-vue@2.1.0_@vitest+ui@3.1.4_eslint@9.27.0_jiti@2.4.2__typescript@5.8.3_vitest@3.1.4_vue@3.5.14_typescript@5.8.3_/node_modules/shadcn-vue/dist/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/shadcn-vue@2.1.0_@vitest+ui@3.1.4_eslint@9.27.0_jiti@2.4.2__typescript@5.8.3_vitest@3.1.4_vue@3.5.14_typescript@5.8.3_/node_modules/shadcn-vue/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/shadcn-vue@2.1.0_@vitest+ui@3.1.4_eslint@9.27.0_jiti@2.4.2__typescript@5.8.3_vitest@3.1.4_vue@3.5.14_typescript@5.8.3_/node_modules:/mnt/c/Users/<USER>/Projects/go/csee/etl/portal/frontend/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../shadcn-vue/dist/index.js" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../shadcn-vue/dist/index.js" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../shadcn-vue/dist/index.js" $args
  } else {
    & "node$exe"  "$basedir/../shadcn-vue/dist/index.js" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
