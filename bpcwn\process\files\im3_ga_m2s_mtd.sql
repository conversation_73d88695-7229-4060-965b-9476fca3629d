select
    format_timestamp ('%Y%m', dt_id) month_id,
    case when dt_id = ${mtd_dt} then 'MTD' else 'LMTD' end period,
    format_timestamp ('%Y%m%d', dt_id) asof_date,
    b.circle,
    b.region_circle region,
    c.flag,
    'IM3' as brand,
    count(distinct b.kabkot_nm) kabu_cnt,
    cast(sum(case when kpi_code = 'acq_ga' then metric else 0 end) as int) ga,
    cast(sum(case when kpi_code = 'acq_qoa_m2s' then metric else 0 end) as int) m2s
from
    `data-bi-prd-935c.bi_dm.hg_kpi_site_wise_dly` a
    left join
    `data-bi-prd-935c.bi_dm.ref_site` b
    on 
        a.site_id = b.site_id
    left join 
    `data-commstrexe-prd-565x.csee_pm.nc_kabu_mapping` c 
    on
        b.kabkot_nm = c.Kabupaten
where
    kpi_code in ('acq_qoa_m2s', 'acq_ga')
and brand = 'IM3'
and a.dt_id in (timestamp(${mtd_dt}), timestamp(date_add(${mtd_dt}, interval -1 month)))
group by
    1, 2, 3, 4, 5, 6

union all

select
    format_timestamp ('%Y%m', dt_id) month_id,
    'FM' period,
    format_timestamp ('%Y%m%d', dt_id) asof_date,
    b.circle,
    b.region_circle region,
    c.flag,
    'IM3' as brand,
    count(distinct b.kabkot_nm) kabu_cnt,
    cast(sum(case when kpi_code = 'acq_ga' then metric else 0 end) as int) ga,
    cast(sum(case when kpi_code = 'acq_qoa_m2s' then metric else 0 end) as int) m2s
from
    `data-bi-prd-935c.bi_dm.hg_kpi_site_wise_dly` a
    left join
    `data-bi-prd-935c.bi_dm.ref_site` b
    on 
        a.site_id = b.site_id
    left join 
    `data-commstrexe-prd-565x.csee_pm.nc_kabu_mapping` c 
    on
        b.kabkot_nm = c.Kabupaten
where
    kpi_code in ('acq_qoa_m2s', 'acq_ga')
and brand = 'IM3'
and dt_id >= timestamp(date_add(${mtd_dt}, interval -6 month))
and dt_id < timestamp(date_trunc(${mtd_dt}, month))
and dt_id = timestamp(date_add(date_add(date_trunc(date(a.dt_id), month), interval 1 month), interval -1 day))
group by
    1, 2, 3, 4, 5, 6